<?php

namespace app\api\controller;

use think\Controller;
use think\Request;
use think\Db;
use think\Log;

/**
 * 自动导入订单API控制器
 * 接收来自Python咸鱼系统的订单数据
 */
class AutoImport extends Controller
{
    /**
     * 自动导入待支付订单
     * POST /api/auto_import_orders
     */
    public function orders(Request $request)
    {
        try {
            // 获取POST数据
            $input = $request->getContent();
            $data = json_decode($input, true);
            
            if (!$data || !isset($data['orders'])) {
                return json([
                    'success' => false,
                    'message' => '无效的数据格式',
                    'code' => 400
                ]);
            }
            
            $orders = $data['orders'];
            $syncTime = $data['sync_time'] ?? time();
            $source = $data['source'] ?? 'unknown';
            
            Log::info("收到咸鱼订单同步请求", [
                'order_count' => count($orders),
                'sync_time' => $syncTime,
                'source' => $source
            ]);
            
            $successCount = 0;
            $errorCount = 0;
            $errors = [];
            
            // 开始事务
            Db::startTrans();
            
            foreach ($orders as $orderData) {
                try {
                    $result = $this->importSingleOrder($orderData);
                    if ($result['success']) {
                        $successCount++;
                    } else {
                        $errorCount++;
                        $errors[] = $result['message'];
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = "订单 {$orderData['tb_order_sn']} 导入失败: " . $e->getMessage();
                    Log::error("导入订单异常", [
                        'order_id' => $orderData['tb_order_sn'] ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            // 提交事务
            Db::commit();
            
            // 记录同步结果
            $this->recordSyncResult($syncTime, $source, $successCount, $errorCount);
            
            return json([
                'success' => true,
                'message' => '订单同步完成',
                'data' => [
                    'total' => count($orders),
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'errors' => $errors
                ],
                'code' => 200
            ]);
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            Log::error("自动导入订单失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return json([
                'success' => false,
                'message' => '系统错误: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }
    
    /**
     * 导入单个订单
     */
    private function importSingleOrder($orderData)
    {
        try {
            // 验证必要字段
            $requiredFields = ['tb_order_sn', 'tb_name', 'tb_qr_url', 'sum'];
            foreach ($requiredFields as $field) {
                if (!isset($orderData[$field]) || empty($orderData[$field])) {
                    return [
                        'success' => false,
                        'message' => "缺少必要字段: {$field}"
                    ];
                }
            }
            
            $orderSn = $orderData['tb_order_sn'];
            
            // 检查订单是否已存在
            $existingOrder = Db::name('product')
                ->where('tb_order_sn', $orderSn)
                ->find();
            
            if ($existingOrder) {
                // 更新现有订单
                $updateData = [
                    'tb_name' => $orderData['tb_name'],
                    'tb_qr_url' => $orderData['tb_qr_url'],
                    'sum' => $orderData['sum'],
                    'remark' => $orderData['remark'] ?? '自动同步更新',
                    'utime' => time()
                ];
                
                Db::name('product')
                    ->where('tb_order_sn', $orderSn)
                    ->update($updateData);
                
                return [
                    'success' => true,
                    'message' => "订单 {$orderSn} 更新成功"
                ];
            } else {
                // 插入新订单
                $insertData = [
                    'tb_order_sn' => $orderSn,
                    'tb_name' => $orderData['tb_name'],
                    'tb_qr_url' => $orderData['tb_qr_url'],
                    'sum' => $orderData['sum'],
                    'admin_id' => 1, // 默认管理员
                    'shop_id' => 1,  // 默认店铺
                    'is_auto' => 1,  // 标记为自动导入
                    'is_sale' => 1,  // 可售
                    'is_expire' => 1, // 未过期
                    'ctime' => time(),
                    'utime' => time(),
                    'remark' => $orderData['remark'] ?? '自动同步导入'
                ];
                
                // 添加可选字段
                if (isset($orderData['buyer_name'])) {
                    $insertData['buyer_name'] = $orderData['buyer_name'];
                }
                if (isset($orderData['seller_name'])) {
                    $insertData['seller_name'] = $orderData['seller_name'];
                }
                if (isset($orderData['expire_time'])) {
                    $insertData['expire_time'] = $orderData['expire_time'];
                }
                
                Db::name('product')->insert($insertData);
                
                return [
                    'success' => true,
                    'message' => "订单 {$orderSn} 导入成功"
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => "导入订单失败: " . $e->getMessage()
            ];
        }
    }
    
    /**
     * 记录同步结果
     */
    private function recordSyncResult($syncTime, $source, $successCount, $errorCount)
    {
        try {
            Db::name('sync_log')->insert([
                'sync_time' => $syncTime,
                'source' => $source,
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'total_count' => $successCount + $errorCount,
                'created_at' => time()
            ]);
        } catch (\Exception $e) {
            Log::error("记录同步结果失败", ['error' => $e->getMessage()]);
        }
    }
    
    /**
     * 获取同步状态
     * GET /api/sync_status
     */
    public function syncStatus()
    {
        try {
            // 获取最近的同步记录
            $recentSyncs = Db::name('sync_log')
                ->order('created_at DESC')
                ->limit(10)
                ->select();
            
            // 获取今日统计
            $todayStart = strtotime(date('Y-m-d 00:00:00'));
            $todayStats = Db::name('sync_log')
                ->where('created_at', '>=', $todayStart)
                ->field('SUM(success_count) as total_success, SUM(error_count) as total_error, COUNT(*) as sync_times')
                ->find();
            
            // 获取自动导入的订单数量
            $autoOrderCount = Db::name('product')
                ->where('is_auto', 1)
                ->count();
            
            return json([
                'success' => true,
                'data' => [
                    'recent_syncs' => $recentSyncs,
                    'today_stats' => $todayStats,
                    'auto_order_count' => $autoOrderCount,
                    'last_sync_time' => $recentSyncs[0]['created_at'] ?? 0
                ],
                'code' => 200
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取同步状态失败: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }
    
    /**
     * 手动触发同步
     * POST /api/trigger_sync
     */
    public function triggerSync()
    {
        try {
            // 调用Python系统的同步API
            $pythonApiUrl = 'http://localhost:8080/api/trigger_auto_sync';
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $pythonApiUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json'
                ],
                CURLOPT_POSTFIELDS => json_encode([
                    'trigger_source' => 'manual',
                    'trigger_time' => time()
                ])
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $result = json_decode($response, true);
                return json([
                    'success' => true,
                    'message' => '同步触发成功',
                    'data' => $result,
                    'code' => 200
                ]);
            } else {
                return json([
                    'success' => false,
                    'message' => '同步触发失败: HTTP ' . $httpCode,
                    'code' => 500
                ]);
            }
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '触发同步失败: ' . $e->getMessage(),
                'code' => 500
            ]);
        }
    }
}

/*
需要创建同步日志表的SQL：

CREATE TABLE `sync_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sync_time` int(11) NOT NULL COMMENT '同步时间戳',
  `source` varchar(50) NOT NULL COMMENT '同步来源',
  `success_count` int(11) DEFAULT 0 COMMENT '成功数量',
  `error_count` int(11) DEFAULT 0 COMMENT '失败数量', 
  `total_count` int(11) DEFAULT 0 COMMENT '总数量',
  `created_at` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_sync_time` (`sync_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单同步日志表';

-- 为product表添加自动导入相关字段
ALTER TABLE `df_product` ADD COLUMN `is_auto` tinyint(1) DEFAULT 0 COMMENT '是否自动导入';
ALTER TABLE `df_product` ADD COLUMN `buyer_name` varchar(100) DEFAULT NULL COMMENT '买家姓名';
ALTER TABLE `df_product` ADD COLUMN `seller_name` varchar(100) DEFAULT NULL COMMENT '卖家姓名';
ALTER TABLE `df_product` ADD COLUMN `expire_time` int(11) DEFAULT NULL COMMENT '过期时间';
ALTER TABLE `df_product` ADD COLUMN `utime` int(11) DEFAULT NULL COMMENT '更新时间';

*/
