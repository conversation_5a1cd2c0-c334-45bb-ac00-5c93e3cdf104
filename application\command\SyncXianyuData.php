<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\lib\XianyuApiClient;

/**
 * 同步咸鱼数据定时任务
 * 用法: php think sync:xianyu
 */
class SyncXianyuData extends Command
{
    protected function configure()
    {
        $this->setName('sync:xianyu')
             ->setDescription('同步咸鱼系统数据到代付系统');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始同步咸鱼数据...');
        
        try {
            $xianyuApi = new XianyuApiClient();
            
            // 1. 测试连接
            if (!$xianyuApi->testConnection()) {
                $output->writeln('<error>无法连接到咸鱼Python系统，请检查服务是否启动</error>');
                return;
            }
            
            $output->writeln('<info>✓ 连接咸鱼系统成功</info>');
            
            // 2. 同步待支付订单
            $this->syncPendingOrders($xianyuApi, $output);
            
            // 3. 同步账号状态
            $this->syncAccountStatus($xianyuApi, $output);
            
            // 4. 自动搜索热门商品（可选）
            $this->autoSearchPopularItems($xianyuApi, $output);
            
            $output->writeln('<info>✓ 咸鱼数据同步完成</info>');
            
        } catch (\Exception $e) {
            $output->writeln('<error>同步失败: ' . $e->getMessage() . '</error>');
        }
    }
    
    /**
     * 同步待支付订单
     */
    private function syncPendingOrders(XianyuApiClient $xianyuApi, Output $output)
    {
        $output->writeln('正在同步待支付订单...');
        
        $pendingOrders = $xianyuApi->getPendingOrders();
        
        if (empty($pendingOrders)) {
            $output->writeln('没有待支付订单');
            return;
        }
        
        $productModel = model('Product');
        $syncCount = 0;
        
        foreach ($pendingOrders as $order) {
            try {
                // 检查订单是否已存在
                $exists = $productModel->where('tb_order_sn', $order['order_id'])->find();
                if ($exists) {
                    continue;
                }
                
                // 获取订单二维码
                $qrUrl = $xianyuApi->getOrderQrCode($order['order_id']);
                if (!$qrUrl) {
                    continue;
                }
                
                // 插入待支付订单
                $data = [
                    'tb_order_sn' => $order['order_id'],
                    'tb_name' => $order['title'] ?? '咸鱼订单',
                    'tb_qr_url' => $qrUrl,
                    'sum' => $order['amount'],
                    'admin_id' => 1,
                    'shop_id' => 1,
                    'is_auto' => 1,
                    'ctime' => time(),
                    'is_sale' => 1,
                    'is_expire' => 1,
                    'remark' => '自动同步的待支付订单'
                ];
                
                if ($productModel->insert($data)) {
                    $syncCount++;
                    $output->writeln("✓ 同步订单: {$order['order_id']} - ¥{$order['amount']}");
                }
                
            } catch (\Exception $e) {
                $output->writeln("<error>同步订单失败: {$order['order_id']} - {$e->getMessage()}</error>");
            }
        }
        
        $output->writeln("<info>同步了 {$syncCount} 个待支付订单</info>");
    }
    
    /**
     * 同步账号状态
     */
    private function syncAccountStatus(XianyuApiClient $xianyuApi, Output $output)
    {
        $output->writeln('正在同步账号状态...');
        
        $accounts = $xianyuApi->getAccountStatus();
        
        if (empty($accounts)) {
            $output->writeln('没有账号信息');
            return;
        }
        
        // 更新或插入账号状态到数据库
        $accountModel = model('XianyuAccount'); // 需要创建这个模型
        
        foreach ($accounts as $account) {
            try {
                $data = [
                    'account_id' => $account['id'],
                    'shop_name' => $account['shop_name'] ?? '',
                    'status' => $account['status'],
                    'last_check' => date('Y-m-d H:i:s', $account['last_check']),
                    'orders_today' => $account['orders_today'] ?? 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                // 使用replace方式更新或插入
                $accountModel->replace($data);
                
                $status = $account['status'] == 'active' ? '正常' : '异常';
                $output->writeln("✓ 账号: {$account['id']} - {$status}");
                
            } catch (\Exception $e) {
                $output->writeln("<error>同步账号失败: {$account['id']} - {$e->getMessage()}</error>");
            }
        }
    }
    
    /**
     * 自动搜索热门商品
     */
    private function autoSearchPopularItems(XianyuApiClient $xianyuApi, Output $output)
    {
        $output->writeln('正在搜索热门商品...');
        
        // 热门搜索关键词
        $keywords = [
            '手机', 'iPhone', '笔记本', '相机', '耳机',
            '游戏机', 'iPad', '手表', '键盘', '鼠标'
        ];
        
        $totalImported = 0;
        
        foreach ($keywords as $keyword) {
            try {
                $items = $xianyuApi->searchItems($keyword, 1, 10); // 每个关键词搜索10个商品
                
                if (!empty($items)) {
                    $imported = $xianyuApi->importItemsToPaymentSystem($items);
                    $totalImported += $imported;
                    
                    $output->writeln("✓ 关键词 '{$keyword}': 导入 {$imported} 个商品");
                }
                
                // 避免请求过于频繁
                sleep(2);
                
            } catch (\Exception $e) {
                $output->writeln("<error>搜索关键词失败: {$keyword} - {$e->getMessage()}</error>");
            }
        }
        
        $output->writeln("<info>总共导入了 {$totalImported} 个热门商品</info>");
    }
}

/**
 * 咸鱼账号模型（需要创建对应的数据表）
 */
class XianyuAccountModel
{
    // 创建表的SQL:
    /*
    CREATE TABLE `xianyu_accounts` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `account_id` varchar(50) NOT NULL COMMENT '账号ID',
        `shop_name` varchar(100) DEFAULT NULL COMMENT '店铺名称',
        `status` varchar(20) DEFAULT 'active' COMMENT '状态：active/expired',
        `last_check` datetime DEFAULT NULL COMMENT '最后检查时间',
        `orders_today` int(11) DEFAULT 0 COMMENT '今日订单数',
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `account_id` (`account_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='咸鱼账号状态表';
    */
}
