<?php

/**
 * 订单查询系统配置
 * 支持多种查询方式的无缝切换
 */

return [
    // 查询模式
    // xianyu: 仅使用咸鱼实时系统
    // agiso: 仅使用阿奇索API
    // hybrid: 混合模式，优先咸鱼，备用阿奇索
    'mode' => 'xianyu',
    
    // 是否启用备用方案
    'fallback_enabled' => true,
    
    // 缓存配置
    'cache_enabled' => true,
    'cache_ttl' => 60,  // 缓存时间（秒）
    
    // 咸鱼系统配置
    'xianyu' => [
        'api_url' => 'http://localhost:8080/api',
        'timeout' => 10,
        'retry_times' => 3,
        'health_check_interval' => 30,  // 健康检查间隔（秒）
        
        // WebSocket配置（用于实时推送）
        'websocket' => [
            'enabled' => true,
            'url' => 'ws://localhost:8080/ws',
            'auto_reconnect' => true
        ],
        
        // 数据同步配置
        'sync' => [
            'enabled' => true,
            'interval' => 5,  // 同步间隔（秒）
            'batch_size' => 100
        ]
    ],
    
    // 阿奇索配置（保持原有配置兼容）
    'agiso' => [
        'app_secret' => '9wwy792tu6uszmwa8us293eyw6z8pw82',
        'trade_url' => 'http://gw.api.agiso.com/alds/Trade/TradeInfo',
        'timeout' => 30,
        'retry_times' => 2
    ],
    
    // 性能优化配置
    'performance' => [
        // 并发查询限制
        'max_concurrent_requests' => 10,
        
        // 批量查询配置
        'batch_query_enabled' => true,
        'batch_size' => 50,
        
        // 连接池配置
        'connection_pool' => [
            'enabled' => true,
            'max_connections' => 20,
            'idle_timeout' => 300
        ]
    ],
    
    // 监控和日志配置
    'monitoring' => [
        'enabled' => true,
        'log_level' => 'info',  // debug, info, warning, error
        
        // 性能监控
        'performance_tracking' => true,
        'slow_query_threshold' => 1000,  // 慢查询阈值（毫秒）
        
        // 错误监控
        'error_tracking' => true,
        'error_notification' => [
            'enabled' => false,
            'webhook_url' => '',
            'threshold' => 10  // 错误次数阈值
        ]
    ],
    
    // 数据格式配置
    'data_format' => [
        // 是否保持阿奇索格式兼容
        'agiso_compatible' => true,
        
        // 是否包含扩展字段
        'include_extra_fields' => true,
        
        // 时间格式
        'datetime_format' => 'Y-m-d H:i:s',
        
        // 金额格式
        'amount_precision' => 2
    ],
    
    // 安全配置
    'security' => [
        // API密钥验证
        'api_key_required' => false,
        'api_key' => '',
        
        // IP白名单
        'ip_whitelist_enabled' => false,
        'ip_whitelist' => [],
        
        // 请求频率限制
        'rate_limit' => [
            'enabled' => false,
            'requests_per_minute' => 1000
        ]
    ],
    
    // 开发和调试配置
    'debug' => [
        'enabled' => false,
        'log_requests' => false,
        'log_responses' => false,
        'mock_data_enabled' => false,
        
        // 测试模式
        'test_mode' => [
            'enabled' => false,
            'mock_orders' => [
                'XY202501010001' => [
                    'order_id' => 'XY202501010001',
                    'status' => 'paid',
                    'amount' => 1299.00,
                    'pay_time' => 1704067200,
                    'create_time' => 1704060000
                ]
            ]
        ]
    ],
    
    // 迁移配置
    'migration' => [
        // 是否启用平滑迁移
        'smooth_migration' => true,
        
        // 迁移阶段
        // phase1: 双写模式，查询仍用阿奇索
        // phase2: 混合模式，优先咸鱼
        // phase3: 完全切换到咸鱼
        'phase' => 'phase2',
        
        // 数据对比验证
        'data_validation' => [
            'enabled' => true,
            'sample_rate' => 0.1,  // 10%的请求进行对比验证
            'tolerance' => 0.01    // 允许的数据差异容忍度
        ]
    ]
];
