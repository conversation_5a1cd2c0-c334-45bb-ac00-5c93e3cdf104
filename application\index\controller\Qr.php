<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use PHPZxing\PHPZxingDecoder;
use think\Config;

class Qr extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';
    protected $pro = null;
    protected $order = null;

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        $this->pro = model('Product');
        $this->order = model('Order');
    }



    public function index(){
        $this->assign('msg', '订单不存在或已过期');
        if ($this->request->isGet()) {
            $order_sn = $this->request->get('order_sn');
            $orderInfo = $this->order->where('order_sn', $order_sn)->find();
            if(!$orderInfo){
                return $this->view->fetch('error');
            }
            if ($orderInfo['expire_time']<time()) {
                $this->assign('msg', '订单已过期');
            }
            $user_agent = $_SERVER['HTTP_USER_AGENT'];
            $agent = strtolower($user_agent);
            $android = strpos($agent,'android');
            $iphone = strpos($agent,'iphone') || strpos($agent, 'ipad');
            if($android){
                $rs = $this->app($orderInfo);
                return $rs?$this->view->fetch('android'):$this->view->fetch('error');
            }elseif($iphone){
                $rs = $this->app($orderInfo);
                return $rs?$this->view->fetch('ios'):$this->view->fetch('error');
            }else{
                $rs = $this->pc($orderInfo);
                return $rs?$this->view->fetch('index'):$this->view->fetch('error');
            }

        }
    }

    //pc端
    private function pc($orderInfo)
    {

        if($orderInfo&&$orderInfo['expire_time'] > time()){
            $proInfo = $this->pro->where('tb_order_sn', $orderInfo['tb_order_sn'])->field('tb_qr,tb_qr_url,tb_name')->find();
            if($proInfo){
                if ($proInfo['tb_qr_url']) {
                    $decodedData = $proInfo['tb_qr_url'];
                } else {
                    $decodedData = self::decodeQr($proInfo['tb_qr']);
                }
                if($decodedData){
                    $this ->assign('qr',$decodedData);
                    $this ->assign('orderInfo',$orderInfo);
                    $this ->assign('tb_name',$proInfo['tb_name']);
                    return true;
                }else{
                    return false;
                }
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    //移动端
    private function app($orderInfo)
    {
        if($orderInfo&&$orderInfo['expire_time'] > time()){
            $proInfo = $this->pro->where('tb_order_sn', $orderInfo['tb_order_sn'])->field('tb_qr,tb_qr_url')->find();
            //print_r($proInfo);exit();
            if($proInfo){
                if ($proInfo['tb_qr_url']) {
                    $decodedData = $proInfo['tb_qr_url'];
                } else {
                    $decodedData = self::decodeQr($proInfo['tb_qr']);
                }
                if($decodedData) {
                    $this->assign('qr_url', $decodedData);
                }
                $this->assign('qr', $decodedData);
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }


    //解析二维码
    private function decodeQr($file){
        $config = array(
            'try_harder' => true,
            'multiple_bar_codes' => true
        );
        $decoder        = new PHPZxingDecoder($config);
        $decoder->setJavaPath(Config::get('jdk'));

        $data = $decoder->decode(ltrim($file,"/"));
        if($data->isFound()) {
            return $data->getImageValue();
            //$data->getFormat();
            //$data->getType();
        }


    }




}
