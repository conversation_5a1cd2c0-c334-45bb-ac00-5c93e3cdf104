<?php

namespace app\lib;

use think\Config;

/**
 * 订单查询管理器
 * 统一管理订单查询，支持多种查询方式切换
 * 无需修改现有代码，只需修改配置
 */
class OrderQueryManager
{
    private $config;
    private $xianyuAdapter;
    
    public function __construct()
    {
        $this->config = Config::get('order_query', [
            'mode' => 'xianyu',           // xianyu | agiso | hybrid
            'fallback_enabled' => true,   // 是否启用备用方案
            'cache_enabled' => true,      // 是否启用缓存
            'cache_ttl' => 60            // 缓存时间（秒）
        ]);
        
        $this->xianyuAdapter = new XianyuOrderAdapter();
    }
    
    /**
     * 统一的订单查询接口
     * 完全兼容现有的阿奇索调用方式
     */
    public function getOrderInfo($orderId, $appSecret = null)
    {
        $mode = $this->config['mode'];
        
        switch ($mode) {
            case 'xianyu':
                return $this->queryFrom<PERSON>ianyu($orderId);
                
            case 'agiso':
                return $this->queryFromAgiso($orderId, $appSecret);
                
            case 'hybrid':
                return $this->queryHybrid($orderId, $appSecret);
                
            default:
                return $this->queryFromXianyu($orderId);
        }
    }
    
    /**
     * 从咸鱼系统查询
     */
    private function queryFromXianyu($orderId)
    {
        try {
            // 先检查咸鱼系统是否可用
            if (!$this->xianyuAdapter->healthCheck()) {
                if ($this->config['fallback_enabled']) {
                    return $this->queryFromAgiso($orderId);
                }
                throw new \Exception('咸鱼系统不可用');
            }
            
            $result = $this->xianyuAdapter->getTradeInfo($orderId);
            
            // 如果咸鱼系统返回错误且启用了备用方案
            if ($this->config['fallback_enabled']) {
                $data = json_decode($result, true);
                if (!$data || $data['Error_Code'] !== 0) {
                    return $this->queryFromAgiso($orderId);
                }
            }
            
            return $result;
            
        } catch (\Exception $e) {
            if ($this->config['fallback_enabled']) {
                return $this->queryFromAgiso($orderId);
            }
            throw $e;
        }
    }
    
    /**
     * 从阿奇索查询（原有方式）
     */
    private function queryFromAgiso($orderId, $appSecret = null)
    {
        // 调用原有的阿奇索查询逻辑
        $agisoConfig = Config::get('agiso');
        $appSecret = $appSecret ?: $agisoConfig['appSecret'];
        $tradeUrl = $agisoConfig['tradeUrl'];
        
        $params = [
            'tid' => $orderId,
            'timestamp' => time(),
        ];
        $params['sign'] = $this->generateAgisoSign($params, $appSecret);
        
        $headers = [
            "Authorization: Bearer " . $this->getAccessToken(),
            "ApiVersion: 1"
        ];
        
        return $this->httpPost($tradeUrl, $params, $headers);
    }
    
    /**
     * 混合查询模式
     * 优先使用咸鱼实时数据，备用阿奇索
     */
    private function queryHybrid($orderId, $appSecret = null)
    {
        // 先尝试咸鱼系统（实时数据）
        try {
            if ($this->xianyuAdapter->healthCheck()) {
                $xianyuResult = $this->xianyuAdapter->getTradeInfo($orderId);
                $data = json_decode($xianyuResult, true);
                
                if ($data && $data['Error_Code'] === 0) {
                    // 咸鱼系统有数据，直接返回
                    return $xianyuResult;
                }
            }
        } catch (\Exception $e) {
            // 咸鱼系统失败，继续尝试阿奇索
        }
        
        // 备用阿奇索查询
        return $this->queryFromAgiso($orderId, $appSecret);
    }
    
    /**
     * 批量查询订单
     */
    public function batchGetOrderInfo($orderIds)
    {
        if ($this->config['mode'] === 'xianyu' || $this->config['mode'] === 'hybrid') {
            // 咸鱼系统支持批量查询
            return $this->xianyuAdapter->batchGetTradeInfo($orderIds);
        }
        
        // 阿奇索需要逐个查询
        $results = [];
        foreach ($orderIds as $orderId) {
            $results[$orderId] = $this->getOrderInfo($orderId);
        }
        
        return $results;
    }
    
    /**
     * 获取系统状态
     */
    public function getSystemStatus()
    {
        $status = [
            'xianyu_available' => $this->xianyuAdapter->healthCheck(),
            'agiso_available' => $this->checkAgisoHealth(),
            'current_mode' => $this->config['mode'],
            'fallback_enabled' => $this->config['fallback_enabled']
        ];
        
        // 获取咸鱼系统统计
        if ($status['xianyu_available']) {
            $status['xianyu_stats'] = $this->xianyuAdapter->getOrderStatistics();
        }
        
        return $status;
    }
    
    /**
     * 检查阿奇索系统健康状态
     */
    private function checkAgisoHealth()
    {
        try {
            $agisoConfig = Config::get('agiso');
            $url = $agisoConfig['tradeUrl'];
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 5,
                CURLOPT_NOBODY => true  // 只检查连接
            ]);
            
            curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            return $httpCode < 400;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 生成阿奇索签名
     */
    private function generateAgisoSign($params, $appSecret)
    {
        ksort($params);
        $string = '';
        foreach ($params as $key => $value) {
            $string .= $key . $value;
        }
        return md5($string . $appSecret);
    }
    
    /**
     * 获取访问令牌（阿奇索）
     */
    private function getAccessToken()
    {
        // 这里应该实现获取阿奇索访问令牌的逻辑
        // 暂时返回空字符串
        return '';
    }
    
    /**
     * HTTP POST请求
     */
    private function httpPost($url, $params, $headers = [])
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($params),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return $response;
    }
}
