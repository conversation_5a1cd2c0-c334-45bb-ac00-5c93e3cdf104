<?php

namespace app\lib;

/**
 * 咸鱼Python系统API客户端
 * 用于与Python咸鱼自动回复系统进行数据交互
 */
class XianyuApiClient
{
    private $apiBaseUrl;
    private $timeout;
    
    public function __construct($apiBaseUrl = 'http://localhost:8080', $timeout = 30)
    {
        $this->apiBaseUrl = rtrim($apiBaseUrl, '/');
        $this->timeout = $timeout;
    }
    
    /**
     * 获取待支付订单列表
     * @return array
     */
    public function getPendingOrders()
    {
        $url = $this->apiBaseUrl . '/api/get_pending_orders';
        $response = $this->httpRequest('POST', $url, []);
        
        if ($response && isset($response['orders'])) {
            return $response['orders'];
        }
        
        return [];
    }
    
    /**
     * 获取订单二维码
     * @param string $orderId 订单ID
     * @return string|null
     */
    public function getOrderQrCode($orderId)
    {
        $url = $this->apiBaseUrl . '/api/get_qr_code';
        $response = $this->httpRequest('POST', $url, ['order_id' => $orderId]);
        
        if ($response && isset($response['qr_url'])) {
            return $response['qr_url'];
        }
        
        return null;
    }
    
    /**
     * 搜索闲鱼商品
     * @param string $keyword 搜索关键词
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function searchItems($keyword, $page = 1, $pageSize = 20)
    {
        $url = $this->apiBaseUrl . '/api/search_items';
        $response = $this->httpRequest('POST', $url, [
            'keyword' => $keyword,
            'page' => $page,
            'page_size' => $pageSize
        ]);
        
        if ($response && isset($response['items'])) {
            return $response['items'];
        }
        
        return [];
    }
    
    /**
     * 获取账号状态
     * @return array
     */
    public function getAccountStatus()
    {
        $url = $this->apiBaseUrl . '/api/account_status';
        $response = $this->httpRequest('GET', $url, []);
        
        if ($response && isset($response['accounts'])) {
            return $response['accounts'];
        }
        
        return [];
    }
    
    /**
     * 添加Cookie到Python系统
     * @param string $cookieId Cookie ID
     * @param string $cookieValue Cookie值
     * @param int $userId 用户ID
     * @return bool
     */
    public function addCookie($cookieId, $cookieValue, $userId = 1)
    {
        $url = $this->apiBaseUrl . '/api/add_cookie';
        $response = $this->httpRequest('POST', $url, [
            'cookie_id' => $cookieId,
            'cookie_value' => $cookieValue,
            'user_id' => $userId
        ]);
        
        return $response && isset($response['success']) && $response['success'];
    }
    
    /**
     * 发送HTTP请求
     * @param string $method 请求方法
     * @param string $url 请求URL
     * @param array $data 请求数据
     * @return array|null
     */
    private function httpRequest($method, $url, $data = [])
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ]
        ]);
        
        if (strtoupper($method) === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            \think\Log::error("XianyuAPI请求错误: " . $error);
            return null;
        }
        
        if ($httpCode !== 200) {
            \think\Log::error("XianyuAPI响应错误: HTTP {$httpCode}");
            return null;
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            \think\Log::error("XianyuAPI响应解析错误: " . json_last_error_msg());
            return null;
        }
        
        return $result;
    }
    
    /**
     * 测试API连接
     * @return bool
     */
    public function testConnection()
    {
        $url = $this->apiBaseUrl . '/health';
        $response = $this->httpRequest('GET', $url, []);
        
        return $response !== null;
    }
    
    /**
     * 批量导入商品到代付系统
     * @param array $items 商品列表
     * @param int $adminId 管理员ID
     * @param int $shopId 店铺ID
     * @return int 成功导入的数量
     */
    public function importItemsToPaymentSystem($items, $adminId = 1, $shopId = 1)
    {
        $productModel = model('Product');
        $successCount = 0;
        
        foreach ($items as $item) {
            try {
                // 提取价格数字
                $price = $this->extractPrice($item['price']);
                if ($price <= 0) {
                    continue;
                }
                
                // 检查是否已存在
                $exists = $productModel->where('tb_order_sn', $item['item_id'])->find();
                if ($exists) {
                    continue;
                }
                
                // 插入商品数据
                $data = [
                    'tb_order_sn' => $item['item_id'],
                    'tb_name' => $item['title'],
                    'tb_qr_url' => $item['item_url'],
                    'sum' => $price,
                    'admin_id' => $adminId,
                    'shop_id' => $shopId,
                    'is_auto' => 1, // 标记为自动导入
                    'ctime' => time(),
                    'is_sale' => 1,
                    'is_expire' => 1
                ];
                
                if ($productModel->insert($data)) {
                    $successCount++;
                }
                
            } catch (\Exception $e) {
                \think\Log::error("导入商品失败: " . $e->getMessage());
                continue;
            }
        }
        
        return $successCount;
    }
    
    /**
     * 从价格字符串中提取数字
     * @param string $priceStr 价格字符串
     * @return float
     */
    private function extractPrice($priceStr)
    {
        // 移除¥符号和其他非数字字符
        $price = preg_replace('/[^\d.]/', '', $priceStr);
        return floatval($price);
    }
}
