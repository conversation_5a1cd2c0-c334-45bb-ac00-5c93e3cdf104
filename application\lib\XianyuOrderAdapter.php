<?php

namespace app\lib;

/**
 * 咸鱼订单查询适配器
 * 将咸鱼系统的实时订单数据适配为阿奇索API格式
 * 实现无缝替换，无需修改现有代码
 */
class XianyuOrderAdapter
{
    private $xianyuApiUrl;
    private $timeout;
    
    public function __construct($apiUrl = 'http://localhost:8080/api', $timeout = 10)
    {
        $this->xianyuApiUrl = $apiUrl;
        $this->timeout = $timeout;
    }
    
    /**
     * 模拟阿奇索API的TradeInfo接口
     * 返回与阿奇索相同格式的数据
     */
    public function getTradeInfo($orderId, $appSecret = null)
    {
        try {
            // 从咸鱼系统获取订单信息
            $xianyuOrder = $this->getOrderFromXianyu($orderId);
            
            if (!$xianyuOrder) {
                // 如果咸鱼系统没有找到，返回阿奇索格式的错误
                return $this->formatAgisoError('订单不存在');
            }
            
            // 将咸鱼订单数据转换为阿奇索格式
            return $this->convertToAgisoFormat($xianyuOrder);
            
        } catch (\Exception $e) {
            return $this->formatAgisoError('查询失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 从咸鱼系统获取订单信息
     */
    private function getOrderFromXianyu($orderId)
    {
        $url = $this->xianyuApiUrl . '/orders/' . $orderId;
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json'
            ]
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                return $data['data'];
            }
        }
        
        return null;
    }
    
    /**
     * 将咸鱼订单格式转换为阿奇索API格式
     */
    private function convertToAgisoFormat($xianyuOrder)
    {
        // 状态映射
        $statusMap = [
            'pending' => null,      // 未支付，PayTime为null
            'paid' => time(),       // 已支付，使用当前时间或实际支付时间
            'closed' => null        // 已关闭，PayTime为null
        ];
        
        $payTime = null;
        if ($xianyuOrder['status'] === 'paid') {
            // 如果有实际支付时间，使用实际时间
            $payTime = isset($xianyuOrder['pay_time']) 
                ? date('Y-m-d H:i:s', $xianyuOrder['pay_time'])
                : date('Y-m-d H:i:s');
        }
        
        // 构造阿奇索格式的响应
        $agisoResponse = [
            'Error_Code' => 0,
            'IsSuccess' => true,
            'Message' => '查询成功',
            'Data' => [
                'OrderId' => $xianyuOrder['order_id'],
                'Amount' => $xianyuOrder['amount'],
                'PayTime' => $payTime,
                'Status' => $this->mapStatus($xianyuOrder['status']),
                'CreateTime' => date('Y-m-d H:i:s', $xianyuOrder['create_time']),
                // 咸鱼系统的额外信息
                'Extra' => [
                    'source' => 'xianyu_real_time',
                    'item_id' => $xianyuOrder['item_id'] ?? '',
                    'user_id' => $xianyuOrder['user_id'] ?? '',
                    'cookie_id' => $xianyuOrder['cookie_id'] ?? '',
                    'real_time' => true
                ]
            ]
        ];
        
        return json_encode($agisoResponse);
    }
    
    /**
     * 状态映射
     */
    private function mapStatus($xianyuStatus)
    {
        $statusMap = [
            'pending' => 'WAIT_BUYER_PAY',
            'paid' => 'TRADE_SUCCESS', 
            'closed' => 'TRADE_CLOSED'
        ];
        
        return $statusMap[$xianyuStatus] ?? 'UNKNOWN';
    }
    
    /**
     * 格式化阿奇索错误响应
     */
    private function formatAgisoError($message)
    {
        $errorResponse = [
            'Error_Code' => 1,
            'IsSuccess' => false,
            'Message' => $message,
            'Data' => null
        ];
        
        return json_encode($errorResponse);
    }
    
    /**
     * 批量查询订单（扩展功能）
     */
    public function batchGetTradeInfo($orderIds)
    {
        $results = [];
        
        foreach ($orderIds as $orderId) {
            $results[$orderId] = $this->getTradeInfo($orderId);
        }
        
        return $results;
    }
    
    /**
     * 获取实时订单统计
     */
    public function getOrderStatistics()
    {
        try {
            $url = $this->xianyuApiUrl . '/orders/statistics';
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $this->timeout
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                return json_decode($response, true);
            }
            
        } catch (\Exception $e) {
            // 静默失败
        }
        
        return null;
    }
    
    /**
     * 健康检查
     */
    public function healthCheck()
    {
        try {
            $url = $this->xianyuApiUrl . '/health';
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 5
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            return $httpCode === 200;
            
        } catch (\Exception $e) {
            return false;
        }
    }
}
