-- 咸鱼代付系统数据库初始化脚本
-- 用于支持咸鱼自动回复系统集成

-- 1. 创建同步日志表
CREATE TABLE IF NOT EXISTS `sync_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sync_time` int(11) NOT NULL COMMENT '同步时间戳',
  `source` varchar(50) NOT NULL COMMENT '同步来源',
  `success_count` int(11) DEFAULT 0 COMMENT '成功数量',
  `error_count` int(11) DEFAULT 0 COMMENT '失败数量', 
  `total_count` int(11) DEFAULT 0 COMMENT '总数量',
  `created_at` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_sync_time` (`sync_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单同步日志表';

-- 2. 检查df_product表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `df_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tb_order_sn` varchar(100) NOT NULL COMMENT '淘宝/咸鱼订单号',
  `tb_name` varchar(255) NOT NULL COMMENT '商品名称',
  `tb_qr` varchar(255) DEFAULT NULL COMMENT '二维码图片路径',
  `tb_qr_url` text COMMENT '二维码对应的URL链接',
  `sum` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `admin_id` int(11) DEFAULT 1 COMMENT '管理员ID',
  `shop_id` int(11) DEFAULT 1 COMMENT '店铺ID',
  `is_sale` tinyint(1) DEFAULT 1 COMMENT '是否可售',
  `is_expire` tinyint(1) DEFAULT 1 COMMENT '是否过期',
  `ctime` int(11) NOT NULL COMMENT '创建时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tb_order_sn` (`tb_order_sn`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品/订单表';

-- 3. 为df_product表添加自动导入相关字段（如果不存在）
ALTER TABLE `df_product` 
ADD COLUMN IF NOT EXISTS `is_auto` tinyint(1) DEFAULT 0 COMMENT '是否自动导入',
ADD COLUMN IF NOT EXISTS `buyer_name` varchar(100) DEFAULT NULL COMMENT '买家姓名',
ADD COLUMN IF NOT EXISTS `seller_name` varchar(100) DEFAULT NULL COMMENT '卖家姓名',
ADD COLUMN IF NOT EXISTS `expire_time` int(11) DEFAULT NULL COMMENT '过期时间',
ADD COLUMN IF NOT EXISTS `utime` int(11) DEFAULT NULL COMMENT '更新时间',
ADD COLUMN IF NOT EXISTS `item_id` varchar(50) DEFAULT NULL COMMENT '咸鱼商品ID',
ADD COLUMN IF NOT EXISTS `user_id` varchar(50) DEFAULT NULL COMMENT '买家用户ID',
ADD COLUMN IF NOT EXISTS `cookie_id` varchar(50) DEFAULT NULL COMMENT '咸鱼账号ID';

-- 4. 添加索引优化查询性能
ALTER TABLE `df_product` 
ADD INDEX IF NOT EXISTS `idx_is_auto` (`is_auto`),
ADD INDEX IF NOT EXISTS `idx_item_id` (`item_id`),
ADD INDEX IF NOT EXISTS `idx_cookie_id` (`cookie_id`),
ADD INDEX IF NOT EXISTS `idx_utime` (`utime`);

-- 5. 创建咸鱼账号管理表（如果需要）
CREATE TABLE IF NOT EXISTS `xianyu_accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cookie_id` varchar(50) NOT NULL COMMENT '账号唯一标识',
  `shop_name` varchar(100) DEFAULT NULL COMMENT '店铺名称',
  `cookies_str` text NOT NULL COMMENT 'Cookie字符串',
  `status` enum('active','expired','disabled') DEFAULT 'active' COMMENT '账号状态',
  `last_check` int(11) DEFAULT NULL COMMENT '最后检查时间',
  `orders_today` int(11) DEFAULT 0 COMMENT '今日订单数',
  `user_id` int(11) DEFAULT NULL COMMENT '关联用户ID',
  `created_at` int(11) NOT NULL COMMENT '创建时间',
  `updated_at` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_cookie_id` (`cookie_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_check` (`last_check`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='咸鱼账号管理表';

-- 6. 创建订单状态变更日志表
CREATE TABLE IF NOT EXISTS `order_status_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(100) NOT NULL COMMENT '订单号',
  `old_status` varchar(20) DEFAULT NULL COMMENT '原状态',
  `new_status` varchar(20) NOT NULL COMMENT '新状态',
  `change_reason` varchar(255) DEFAULT NULL COMMENT '变更原因',
  `source` varchar(50) DEFAULT NULL COMMENT '变更来源',
  `created_at` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_sn` (`order_sn`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单状态变更日志表';

-- 7. 插入测试数据（可选）
INSERT IGNORE INTO `sync_log` (`sync_time`, `source`, `success_count`, `error_count`, `total_count`, `created_at`) 
VALUES 
(UNIX_TIMESTAMP(), 'system_init', 0, 0, 0, UNIX_TIMESTAMP());

-- 8. 创建视图，方便查询自动导入的订单
CREATE OR REPLACE VIEW `v_auto_orders` AS
SELECT 
    p.id,
    p.tb_order_sn,
    p.tb_name,
    p.sum,
    p.item_id,
    p.cookie_id,
    p.buyer_name,
    p.is_auto,
    p.ctime,
    p.utime,
    xa.shop_name,
    xa.status as account_status
FROM df_product p
LEFT JOIN xianyu_accounts xa ON p.cookie_id = xa.cookie_id
WHERE p.is_auto = 1
ORDER BY p.ctime DESC;

-- 9. 创建存储过程，用于批量更新订单状态
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `UpdateOrderStatus`(
    IN p_order_sn VARCHAR(100),
    IN p_new_status VARCHAR(20),
    IN p_reason VARCHAR(255),
    IN p_source VARCHAR(50)
)
BEGIN
    DECLARE v_old_status VARCHAR(20) DEFAULT NULL;
    
    -- 获取当前状态（这里假设有status字段，根据实际情况调整）
    -- SELECT status INTO v_old_status FROM df_order WHERE order_sn = p_order_sn;
    
    -- 记录状态变更日志
    INSERT INTO order_status_log (order_sn, old_status, new_status, change_reason, source, created_at)
    VALUES (p_order_sn, v_old_status, p_new_status, p_reason, p_source, UNIX_TIMESTAMP());
    
    -- 这里可以添加实际的状态更新逻辑
    -- UPDATE df_order SET status = p_new_status WHERE order_sn = p_order_sn;
    
END //
DELIMITER ;

-- 10. 创建函数，用于生成订单号
DELIMITER //
CREATE FUNCTION IF NOT EXISTS `GenerateOrderSN`(p_user_id VARCHAR(50))
RETURNS VARCHAR(100)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_timestamp BIGINT;
    DECLARE v_user_suffix VARCHAR(10);
    DECLARE v_order_sn VARCHAR(100);
    
    SET v_timestamp = UNIX_TIMESTAMP();
    SET v_user_suffix = RIGHT(p_user_id, 6);
    SET v_order_sn = CONCAT('XY', v_timestamp, v_user_suffix);
    
    RETURN v_order_sn;
END //
DELIMITER ;

-- 完成提示
SELECT 
    '数据库初始化完成！' as message,
    '请检查以下表是否创建成功：' as note,
    'sync_log, df_product, xianyu_accounts, order_status_log' as tables,
    '请检查视图：v_auto_orders' as views,
    '请检查存储过程：UpdateOrderStatus' as procedures,
    '请检查函数：GenerateOrderSN' as functions;
