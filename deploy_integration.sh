#!/bin/bash

# 咸鱼代付系统与xianyu-auto-reply系统一键部署脚本
# 适用于同服务器部署方案

echo "🚀 咸鱼系统集成部署脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/redhat-release ]]; then
        OS="centos"
        PKG_MANAGER="yum"
    elif [[ -f /etc/debian_version ]]; then
        OS="ubuntu"
        PKG_MANAGER="apt"
    else
        log_error "不支持的操作系统"
        exit 1
    fi
    log_info "检测到操作系统: $OS"
}

# 安装基础环境
install_base_packages() {
    log_step "安装基础软件包..."
    
    if [[ $OS == "centos" ]]; then
        $PKG_MANAGER update -y
        $PKG_MANAGER install -y epel-release
        $PKG_MANAGER install -y wget curl unzip git
        $PKG_MANAGER install -y php php-mysql php-curl php-json php-gd php-mbstring php-xml
        $PKG_MANAGER install -y python3 python3-pip
        $PKG_MANAGER install -y mysql-server nginx
    elif [[ $OS == "ubuntu" ]]; then
        $PKG_MANAGER update -y
        $PKG_MANAGER install -y wget curl unzip git
        $PKG_MANAGER install -y php php-mysql php-curl php-json php-gd php-mbstring php-xml
        $PKG_MANAGER install -y python3 python3-pip
        $PKG_MANAGER install -y mysql-server nginx
    fi
    
    log_info "基础软件包安装完成"
}

# 配置目录结构
setup_directories() {
    log_step "创建目录结构..."
    
    # PHP系统目录
    mkdir -p /var/www/html/
    
    # Python系统目录
    mkdir -p /opt/xianyu-auto-reply/
    mkdir -p /opt/xianyu-auto-reply/logs/
    
    # 日志目录
    mkdir -p /var/log/xianyu-integration/
    
    log_info "目录结构创建完成"
}

# 部署PHP代付系统
deploy_php_system() {
    log_step "部署PHP代付系统..."
    
    cd /var/www/html/
    
    # 设置权限
    chown -R www-data:www-data . 2>/dev/null || chown -R nginx:nginx . 2>/dev/null || chown -R apache:apache .
    chmod -R 755 .
    
    # 创建必要目录
    mkdir -p runtime/log runtime/cache runtime/temp
    mkdir -p public/uploads
    chmod -R 777 runtime/ public/uploads/
    
    log_info "PHP系统部署完成"
}

# 部署Python监控系统
deploy_python_system() {
    log_step "部署Python监控系统..."
    
    cd /opt/xianyu-auto-reply/
    
    # 安装Python依赖 (如果有requirements.txt)
    if [[ -f requirements.txt ]]; then
        pip3 install -r requirements.txt
        log_info "Python依赖安装完成"
    else
        log_warn "未找到requirements.txt，请手动安装依赖"
    fi
    
    # 设置权限
    chmod +x *.py 2>/dev/null || true
    
    log_info "Python系统部署完成"
}

# 配置数据库
setup_database() {
    log_step "配置数据库..."
    
    # 启动MySQL服务
    systemctl start mysql 2>/dev/null || systemctl start mysqld 2>/dev/null || service mysql start
    systemctl enable mysql 2>/dev/null || systemctl enable mysqld 2>/dev/null || true
    
    log_info "数据库服务已启动"
    log_warn "请手动执行以下步骤:"
    echo "1. 运行: mysql_secure_installation"
    echo "2. 创建数据库: CREATE DATABASE xianyu_payment;"
    echo "3. 导入数据: mysql -u root -p xianyu_payment < xydf.sql"
    echo "4. 配置application/database.php文件"
}

# 配置Web服务器
setup_webserver() {
    log_step "配置Web服务器..."
    
    # 配置Nginx
    cat > /etc/nginx/sites-available/xianyu-payment 2>/dev/null << 'EOF' || cat > /etc/nginx/conf.d/xianyu-payment.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    root /var/www/html/public;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
EOF

    # 启用站点 (Ubuntu)
    if [[ $OS == "ubuntu" ]]; then
        ln -sf /etc/nginx/sites-available/xianyu-payment /etc/nginx/sites-enabled/ 2>/dev/null || true
        rm -f /etc/nginx/sites-enabled/default 2>/dev/null || true
    fi
    
    # 启动服务
    systemctl start nginx php-fpm 2>/dev/null || systemctl start nginx php7.4-fpm 2>/dev/null || true
    systemctl enable nginx php-fpm 2>/dev/null || systemctl enable nginx php7.4-fpm 2>/dev/null || true
    
    log_info "Web服务器配置完成"
}

# 配置系统对接
setup_integration() {
    log_step "配置系统对接..."
    
    # 创建集成配置文件
    cat > /opt/xianyu-auto-reply/integration_config.py << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 同服务器部署配置
PAYMENT_SYSTEM_CONFIG = {
    'api_url': 'http://localhost/api/auto_import_orders',
    'backup_api_url': 'http://127.0.0.1/api/auto_import_orders',
    'timeout': 10,
    'retry_times': 3,
    'retry_delay': 1,
    'enabled': True
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'file': '/var/log/xianyu-integration/integration.log',
    'max_size': '10MB',
    'backup_count': 5
}

# 监控配置
MONITOR_CONFIG = {
    'health_check_interval': 60,
    'order_sync_delay': 2,
    'max_retry_queue_size': 100
}
EOF

    log_info "系统对接配置完成"
}

# 创建系统服务
create_systemd_service() {
    log_step "创建系统服务..."
    
    cat > /etc/systemd/system/xianyu-auto-reply.service << 'EOF'
[Unit]
Description=Xianyu Auto Reply System
After=network.target mysql.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/xianyu-auto-reply
ExecStart=/usr/bin/python3 Start.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable xianyu-auto-reply
    
    log_info "系统服务创建完成"
}

# 测试系统
test_system() {
    log_step "测试系统..."
    
    # 测试Web服务器
    if curl -s http://localhost/ > /dev/null; then
        log_info "✅ Web服务器测试通过"
    else
        log_warn "⚠️ Web服务器测试失败"
    fi
    
    # 测试PHP
    if php -v > /dev/null 2>&1; then
        log_info "✅ PHP环境测试通过"
    else
        log_warn "⚠️ PHP环境测试失败"
    fi
    
    # 测试Python
    if python3 -c "import requests" > /dev/null 2>&1; then
        log_info "✅ Python环境测试通过"
    else
        log_warn "⚠️ Python环境测试失败，可能需要安装requests库"
    fi
    
    # 测试MySQL
    if systemctl is-active mysql > /dev/null 2>&1 || systemctl is-active mysqld > /dev/null 2>&1; then
        log_info "✅ MySQL服务测试通过"
    else
        log_warn "⚠️ MySQL服务测试失败"
    fi
}

# 显示部署结果
show_deployment_result() {
    echo ""
    echo "🎉 部署完成！"
    echo "=================================="
    echo ""
    echo "📁 目录结构:"
    echo "├── /var/www/html/ (咸鱼代付系统)"
    echo "├── /opt/xianyu-auto-reply/ (咸鱼自动回复系统)"
    echo "└── /var/log/xianyu-integration/ (集成日志)"
    echo ""
    echo "🔗 访问地址:"
    echo "• 管理后台: http://localhost/ZLTchzxFfK.php"
    echo "• API测试: http://localhost/test_api.php"
    echo "• 系统分析: http://localhost/integration_deployment_guide.php"
    echo ""
    echo "⚠️ 后续步骤:"
    echo "1. 配置数据库连接 (application/database.php)"
    echo "2. 导入数据库结构 (xydf.sql)"
    echo "3. 配置咸鱼Cookie (global_config.yml)"
    echo "4. 启动Python系统: systemctl start xianyu-auto-reply"
    echo "5. 测试API对接功能"
    echo ""
    echo "📚 文档位置:"
    echo "• 部署指南: /var/www/html/public/integration_deployment_guide.php"
    echo "• 集成配置: /opt/xianyu-auto-reply/xianyu_integration_config.py"
    echo ""
}

# 主函数
main() {
    echo "开始部署咸鱼系统集成环境..."
    echo ""
    
    check_root
    detect_os
    install_base_packages
    setup_directories
    deploy_php_system
    deploy_python_system
    setup_database
    setup_webserver
    setup_integration
    create_systemd_service
    test_system
    show_deployment_result
    
    log_info "部署脚本执行完成！"
}

# 执行主函数
main "$@"
