# 🚀 咸鱼代付系统部署和测试指南

## 📋 部署清单

### 1. 服务器环境要求
- **PHP**: 7.1+ (推荐7.4+)
- **MySQL**: 5.6+ (推荐8.0+)
- **Python**: 3.7+ (用于咸鱼自动回复系统)
- **Web服务器**: Apache/Nginx
- **扩展**: curl, json, pdo_mysql, gd, mbstring

### 2. 文件上传清单

#### PHP代付系统文件 (上传到网站根目录)
```
├── application/          # 核心应用代码
├── public/              # 公共文件
│   ├── payment_flow_analysis.php     # 支付流程分析页面
│   ├── xianyu_system_integration.php # 系统集成方案
│   └── demo/            # API调用示例
├── thinkphp/            # ThinkPHP框架
├── vendor/              # 第三方库
├── database_setup.sql   # 数据库初始化脚本
└── xydf.sql            # 原始数据库结构
```

#### Python咸鱼系统文件 (单独目录)
```
├── xianyu-auto-reply-main/
│   ├── XianyuAutoAsync.py
│   ├── global_config.yml
│   ├── requirements.txt
│   └── ...
└── xianyu_integration_config.py  # 集成配置文件
```

## 🗄️ 数据库配置

### 1. 创建数据库
```sql
CREATE DATABASE xianyu_payment CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 导入数据库结构
```bash
# 方法1: 导入原始结构
mysql -u用户名 -p密码 xianyu_payment < xydf.sql

# 方法2: 运行初始化脚本
mysql -u用户名 -p密码 xianyu_payment < database_setup.sql
```

### 3. 配置数据库连接
编辑 `application/database.php`:
```php
return [
    'type'            => 'mysql',
    'hostname'        => 'localhost',
    'database'        => 'xianyu_payment',
    'username'        => '你的数据库用户名',
    'password'        => '你的数据库密码',
    'hostport'        => '3306',
    'charset'         => 'utf8mb4',
    // ... 其他配置
];
```

## 🔧 系统配置

### 1. PHP系统配置
编辑 `application/config.php`:
```php
return [
    'app_debug' => true,  // 调试模式，生产环境改为false
    'url_route_on' => true,
    'default_timezone' => 'Asia/Shanghai',
    // ... 其他配置
];
```

### 2. 设置目录权限
```bash
chmod -R 755 public/
chmod -R 777 runtime/
chmod -R 777 public/uploads/
```

## 🧪 测试步骤

### 第一步：测试PHP系统基础功能

#### 1.1 访问管理后台
```
URL: http://你的域名/ZLTchzxFfK.php
用户名: admin
密码: admin123321 (或查看数据库df_admin表)
```

#### 1.2 测试API接口
```bash
# 测试下单API
curl -X POST http://你的域名/api/order \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "money=30.00&part_sn=TEST_$(date +%s)&notify=http://test.com/callback&id=1&sign=计算的签名值"
```

#### 1.3 查看分析页面
```
http://你的域名/payment_flow_analysis.php
http://你的域名/xianyu_system_integration.php
```

### 第二步：部署Python咸鱼系统

#### 2.1 安装Python依赖
```bash
cd xianyu-auto-reply-main/xianyu-auto-reply-main/
pip install -r requirements.txt
```

#### 2.2 配置咸鱼Cookie
编辑 `global_config.yml`:
```yaml
COOKIES_STR: "你的咸鱼Cookie字符串"

# 添加代付系统集成配置
PAYMENT_SYSTEM:
  enabled: true
  api_url: "http://你的域名/api/auto_import_orders"
  timeout: 10
  retry_times: 3
```

#### 2.3 集成代付系统
将 `xianyu_integration_config.py` 复制到Python系统目录，然后修改 `XianyuAutoAsync.py`:

```python
# 在文件顶部添加
from xianyu_integration_config import PaymentSystemIntegration

# 在XianyuLive类的__init__方法中添加
self.payment_integration = PaymentSystemIntegration(
    api_url="http://你的域名/api/auto_import_orders"
)
```

### 第三步：端到端测试

#### 3.1 启动Python系统
```bash
python Start.py
```

#### 3.2 模拟咸鱼订单
在咸鱼发送测试消息，观察Python系统日志输出。

#### 3.3 检查数据同步
查看PHP系统数据库 `df_product` 表是否有新增记录：
```sql
SELECT * FROM df_product WHERE is_auto = 1 ORDER BY ctime DESC LIMIT 10;
```

## 📊 监控和调试

### 1. 日志文件位置
```
PHP系统日志: runtime/log/
Python系统日志: logs/xianyu_*.log
```

### 2. 常用调试SQL
```sql
-- 查看自动导入的订单
SELECT * FROM df_product WHERE is_auto = 1;

-- 查看同步日志
SELECT * FROM sync_log ORDER BY created_at DESC;

-- 查看系统订单
SELECT * FROM df_order ORDER BY ctime DESC LIMIT 10;

-- 查看管理员信息
SELECT id, username, app_secret FROM df_admin;
```

### 3. API测试工具
创建 `test_api.php`:
```php
<?php
// API测试脚本
$api_url = 'http://你的域名/api/order';
$app_secret = '从df_admin表获取';

$data = [
    'money' => '30.00',
    'part_sn' => 'TEST_' . time(),
    'notify' => 'http://test.com/callback',
    'id' => '1'
];

// 生成签名
ksort($data);
$str = '';
foreach ($data as $key => $value) {
    $str .= ($key . $value);
}
$str = $app_secret . $str . $app_secret;
$data['sign'] = md5($str);

// 发送请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

echo "请求参数:\n";
print_r($data);
echo "\n响应结果:\n";
echo $response;
?>
```

## ⚠️ 注意事项

### 1. 安全配置
- 生产环境关闭调试模式
- 设置强密码
- 配置HTTPS
- 限制API访问频率

### 2. 性能优化
- 开启PHP OPcache
- 配置MySQL查询缓存
- 使用Redis缓存（可选）

### 3. 备份策略
- 定期备份数据库
- 备份重要配置文件
- 监控磁盘空间

## 🔗 快速链接

部署完成后，可以访问以下页面：

- **管理后台**: `http://你的域名/ZLTchzxFfK.php`
- **支付流程分析**: `http://你的域名/payment_flow_analysis.php`
- **集成方案**: `http://你的域名/xianyu_system_integration.php`
- **API测试**: `http://你的域名/test_api.php`

## 📞 技术支持

如果遇到问题，请检查：
1. 服务器错误日志
2. PHP错误日志
3. 数据库连接状态
4. 文件权限设置
5. 网络连通性

记录详细的错误信息，便于问题排查。
