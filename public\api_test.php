<?php
/**
 * API接口测试工具
 * 用于测试项目中配置的外部API是否有效
 */

echo "<h2>API接口测试工具</h2>";

// 从配置文件中提取的API地址
$apis = [
    '阿奇索订单查询API' => 'http://gw.api.agiso.com/alds/Trade/TradeInfo',
    '演示API地址' => 'http://www.xydaifu.com/api/order',
    'FastAdmin官方API' => 'https://api.fastadmin.net'
];

// 测试API连通性
function testApiConnection($url, $timeout = 10) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $start_time = microtime(true);
    $response = curl_exec($ch);
    $end_time = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    curl_close($ch);
    
    return [
        'success' => $response !== false && empty($error),
        'http_code' => $http_code,
        'response_time' => $response_time,
        'error' => $error,
        'response' => $response
    ];
}

// 测试阿奇索API（带参数）
function testAgisoApi() {
    $url = 'http://gw.api.agiso.com/alds/Trade/TradeInfo';
    $appSecret = '9wwy792tu6uszmwa8us293eyw6z8pw82';
    
    // 模拟参数
    $params = [
        'tid' => 'test123456',
        'timestamp' => time()
    ];
    
    // 生成签名
    ksort($params);
    $str = '';
    foreach ($params as $key => $value) {
        $str .= ($key . $value);
    }
    $str = $appSecret . $str . $appSecret;
    $params['sign'] = md5($str);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer test_token',
        'ApiVersion: 1'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => $response !== false && empty($error),
        'http_code' => $http_code,
        'error' => $error,
        'response' => $response
    ];
}

echo "<h3>基础连通性测试</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>API名称</th><th>地址</th><th>状态</th><th>HTTP状态码</th><th>响应时间</th><th>错误信息</th></tr>";

foreach ($apis as $name => $url) {
    $result = testApiConnection($url);
    
    $status_color = $result['success'] ? 'green' : 'red';
    $status_text = $result['success'] ? '✓ 可访问' : '✗ 无法访问';
    
    echo "<tr>";
    echo "<td>{$name}</td>";
    echo "<td style='font-family: monospace; font-size: 12px;'>{$url}</td>";
    echo "<td style='color: {$status_color}; font-weight: bold;'>{$status_text}</td>";
    echo "<td>{$result['http_code']}</td>";
    echo "<td>{$result['response_time']}ms</td>";
    echo "<td style='color: red;'>{$result['error']}</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>阿奇索API详细测试</h3>";
$agiso_result = testAgisoApi();

echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
echo "<strong>测试结果:</strong><br>";
echo "状态: " . ($agiso_result['success'] ? '<span style="color: green;">成功</span>' : '<span style="color: red;">失败</span>') . "<br>";
echo "HTTP状态码: {$agiso_result['http_code']}<br>";
if ($agiso_result['error']) {
    echo "错误: <span style='color: red;'>{$agiso_result['error']}</span><br>";
}
echo "<strong>响应内容:</strong><br>";
echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow: auto;'>";
echo htmlspecialchars($agiso_result['response']);
echo "</pre>";
echo "</div>";

echo "<h3>域名解析测试</h3>";
$domains = [
    'gw.api.agiso.com',
    'www.xydaifu.com',
    'api.fastadmin.net'
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>域名</th><th>IP地址</th><th>状态</th></tr>";

foreach ($domains as $domain) {
    $ip = gethostbyname($domain);
    $resolved = ($ip !== $domain);
    
    echo "<tr>";
    echo "<td>{$domain}</td>";
    echo "<td>{$ip}</td>";
    echo "<td style='color: " . ($resolved ? 'green' : 'red') . ";'>";
    echo $resolved ? '✓ 解析成功' : '✗ 解析失败';
    echo "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>配置建议</h3>";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0;'>";
echo "<h4>🔍 分析结果:</h4>";
echo "<ul>";
echo "<li><strong>阿奇索API</strong>: 这是系统用来查询淘宝订单状态的第三方接口</li>";
echo "<li><strong>演示API</strong>: 这是一个示例地址，可能已经失效</li>";
echo "<li><strong>FastAdmin API</strong>: 这是框架官方API，用于更新检查等</li>";
echo "</ul>";

echo "<h4>⚠️ 注意事项:</h4>";
echo "<ul>";
echo "<li>如果阿奇索API无法访问，系统将无法查询订单支付状态</li>";
echo "<li>需要联系API提供商获取最新的接口地址和密钥</li>";
echo "<li>建议在后台配置中更新API地址</li>";
echo "</ul>";
echo "</div>";

echo "<h3>手动测试</h3>";
echo "<p>你也可以手动测试API:</p>";
echo "<form method='post'>";
echo "<label>API地址: </label>";
echo "<input type='url' name='test_url' style='width: 400px; padding: 5px;' placeholder='输入要测试的API地址'>";
echo "<button type='submit' style='padding: 5px 10px;'>测试</button>";
echo "</form>";

if (isset($_POST['test_url'])) {
    $test_url = $_POST['test_url'];
    $test_result = testApiConnection($test_url);
    
    echo "<h4>测试结果:</h4>";
    echo "<div style='border: 1px solid #ccc; padding: 10px;'>";
    echo "URL: {$test_url}<br>";
    echo "状态: " . ($test_result['success'] ? '<span style="color: green;">可访问</span>' : '<span style="color: red;">无法访问</span>') . "<br>";
    echo "HTTP状态码: {$test_result['http_code']}<br>";
    echo "响应时间: {$test_result['response_time']}ms<br>";
    if ($test_result['error']) {
        echo "错误: <span style='color: red;'>{$test_result['error']}</span><br>";
    }
    echo "</div>";
}
?>
