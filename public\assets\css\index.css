html,
body {
    height: 100%;
    width: 100%;
}
body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: "Roboto", "SF Pro SC", "SF Pro Display", "SF Pro Icons", "PingFang SC", BlinkMacSystemFont, -apple-system, "Segoe UI", "Microsoft Yahei", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    font-weight: 400;
}
a {
    -webkit-transition: all 0.35s;
    -moz-transition: all 0.35s;
    transition: all 0.35s;
    color: #fdcc52;
}
a:hover,
a:focus {
    color: #fcbd20;
}
hr {
    max-width: 100px;
    margin: 25px auto 0;
    border-width: 1px;
    border-color: rgba(34, 34, 34, 0.1);
}
hr.light {
    border-color: white;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 200;
    letter-spacing: 1px;
}
p {
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 20px;
}
.navbar-default {
    font-family: 'Catamaran', 'Helvetica', 'Arial', 'sans-serif';
    font-weight: 200;
    letter-spacing: 1px;
}
.navbar-default .navbar-header .navbar-brand {
    font-family: 'Catamaran', 'Helvetica', 'Arial', 'sans-serif';
    font-weight: 200;
    letter-spacing: 1px;
}
.navbar-default .navbar-header .navbar-brand:hover,
.navbar-default .navbar-header .navbar-brand:focus {
    color: #fcbd20;
}
.navbar-default .navbar-header .navbar-toggle {
    font-size: 12px;
    color: #222222;
    padding: 8px 10px;
}
.navbar-default .nav > li > a {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: 14px;
}
.navbar-default .nav > li > a:hover,
.navbar-default .nav > li > a:focus:hover {
    color: #fdcc52;
}
.navbar-default .nav > li.active > a,
.navbar-default .nav > li.active > a:focus {
    color: #fdcc52 !important;
    background-color: transparent;
}
.navbar-default .nav > li.active > a:hover,
.navbar-default .nav > li.active > a:focus:hover {
    background-color: transparent;
}
@media (min-width: 768px) {
    .navbar-default {
        background-color: transparent;
        border-color: transparent;
    }
    .navbar-default .navbar-header .navbar-brand {
        color: rgba(255, 255, 255, 0.7);
    }
    .navbar-default .navbar-header .navbar-brand:hover,
    .navbar-default .navbar-header .navbar-brand:focus {
        color: white;
    }
    .navbar-default .nav > li > a,
    .navbar-default .nav > li > a:focus {
        color: rgba(255, 255, 255, 0.7);
    }
    .navbar-default .nav > li > a:hover,
    .navbar-default .nav > li > a:focus:hover {
        color: white;
    }
}
header {
    position: relative;
    width: 100%;
    min-height: auto;
    overflow-y: hidden;
    background: url("../img/bg-pattern.png"), #247cdc;
    /* fallback for old browsers */
    background: url("../img/bg-pattern.png"), -webkit-linear-gradient(to left, #328944, #247cdc);
    /* Chrome 10-25, Safari 5.1-6 */
    background: url("../img/bg-pattern.png"), linear-gradient(to left, #328944, #247cdc);
    /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
    color: white;
}
header .header-content {
    text-align: center;
    padding: 150px 0 50px;
    position: relative;
}
header .header-content .header-content-inner {
    position: relative;
    margin: 0 auto;
}
header .header-content .header-content-inner h1 {
    margin-top: 0;
    margin-bottom: 30px;
    font-size: 80px;
}
header .header-content .header-content-inner .list-badges {
    margin-bottom: 25px;
}
header .header-content .header-content-inner .list-badges img {
    height: 50px;
    margin-bottom: 25px;
}
header .device-container {
    max-width: 300px;
    margin: 0 auto 100px;
}
header .device-container .screen img {
    border-radius: 3px;
}
@media (min-width: 768px) {
    header {
        min-height: 100%;
    }
    header .header-content {
        text-align: center;
        padding: 0;
        height: 100vh;
    }
    header .header-content .header-content-inner {
        width:100%;
        margin: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }
    header .header-content .header-content-inner h1 {
        font-size: 35px;
    }
    header .device-container {
        max-width: none;
        max-height: calc(0vh);
        margin: 100px auto 0;
    }
}
@media (min-width: 992px) {
    header .header-content .header-content-inner h1 {
        font-size: 80px;
    }
}

@media (max-width: 767px) {
    header .header-content .header-content-inner h1 {
        font-size: 50px;
    }
}
section {
    padding: 100px 0;
}
section h2 {
    font-size: 50px;
}
footer {
    background-color: #222222;
    padding: 25px 0;
    color: rgba(255, 255, 255, 0.3);
    text-align: center;
}
footer p {
    font-size: 12px;
    margin: 0;
}
footer ul {
    margin-bottom: 0;
}
footer ul li a {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.3);
}
footer ul li a:hover,
footer ul li a:focus,
footer ul li a:active,
footer ul li a.active {
    text-decoration: none;
}
.bg-primary {
    background: #fdcc52;
    background: -webkit-linear-gradient(#fdcc52, #fdc539);
    background: linear-gradient(#fdcc52, #fdc539);
}
.text-primary {
    color: #fdcc52;
}
.no-gutter > [class*='col-'] {
    padding-right: 0;
    padding-left: 0;
}
.btn-outline {
    color: white;
    border-color: white;
    border: 1px solid;
}
.btn-outline:hover,
.btn-outline:focus,
.btn-outline:active,
.btn-outline.active {
    color: white;
    background-color: #fdcc52;
    border-color: #fdcc52;
}
.btn {
    text-transform: uppercase;
    letter-spacing: 2px;
    border-radius: 300px;
}
.btn-xl {
    margin-top:20px;
    padding: 10px 45px;
    font-size: 14px;
}
