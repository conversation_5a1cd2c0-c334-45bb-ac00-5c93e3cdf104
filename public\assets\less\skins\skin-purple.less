/*
 * Skin: Purple
 * ------------
 */
@import "../bootstrap-less/mixins.less";
@import "../bootstrap-less/variables.less";
@import "../fastadmin/variables.less";
@import "../fastadmin/mixins.less";

.skin-purple {
  //Navbar
  .main-header {
    .navbar {
      .navbar-variant(@purple; #fff);
      .sidebar-toggle {
        color: #fff;
        &:hover {
          background-color: darken(@purple, 5%);
        }
      }
      @media (max-width: @screen-header-collapse) {
        .dropdown-menu {
          li {
            &.divider {
              background-color: rgba(255, 255, 255, 0.1);
            }
            a {
              color: #fff;
              &:hover {
                background: darken(@purple, 5%);
              }
            }
          }
        }
      }
    }
    //Logo
    .logo {
      .logo-variant(darken(@purple, 5%));
    }

    li.user-header {
      background-color: @purple;
    }
  }

  //Content Header
  .content-header {
    background: transparent;
  }

  //Create the sidebar skin
  .skin-dark-sidebar(@purple);
}
