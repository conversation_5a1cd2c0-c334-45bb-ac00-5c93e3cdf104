<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<title>tag-demo</title>
<script src="../../dist/template-native.js"></script>
</head>

<body>
<h1>自定义界定符</h1>
<script id="test" type="text/html">
<!--[if (title) {]-->
    <h3><!--[= title]--></h3>
<!--[} else {]-->
    <h3>无标题</h3>
<!--[}]-->
<ul>
    <!--[for (var i = 0; i < list.length; i ++) {]-->
        <li>索引 <!--[= i + 1 ]--> ：<!--[= list[i]]--></li>
    <!--[}]-->
</ul>

</script>

<script>
template.defaults.openTag = '<!--[';
template.defaults.closeTag = ']-->';


var html = '';
var data = {
    title: '我的标签',
	list: ['文艺', '博客', '摄影', '电影', '民谣', '旅行', '吉他']
};


html = template('test', data);
document.write(html);

</script>
</body>
</html>