/*!
 * Bootstrap-select v1.11.2 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2016 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
!function(a,b){"function"==typeof define&&define.amd?define(["jquery"],function(a){return b(a)}):"object"==typeof exports?module.exports=b(require("jquery")):b(jQuery)}(this,function(a){!function(a){a.fn.selectpicker.defaults={noneSelectedText:"Tidak ada yang dipilih",noneResultsText:"Tidak ada yang cocok {0}",countSelectedText:"{0} terpilih",maxOptionsText:["Mencapai batas (maksimum {n})","Mencapai batas grup (maksimum {n})"],selectAllText:"<PERSON><PERSON><PERSON>mu<PERSON>",deselectAllText:"<PERSON><PERSON> Semua",multipleSeparator:", "}}(a)});