{"name": "bootstrap-table", "homepage": "https://github.com/wenzhixin/bootstrap-table", "authors": ["zhixin <<EMAIL>>"], "description": "An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features.", "main": ["src/bootstrap-table.js", "src/bootstrap-table.css"], "keywords": ["bootstrap", "table", "bootstrap table"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", "docs", "assets"], "version": "1.11.3", "_release": "1.11.3", "_resolution": {"type": "version", "tag": "1.11.3", "commit": "8aa6944b83b1267a9ef4c84bd4f9982a2975ddc9"}, "_source": "https://github.com/karsonzhang/fastadmin-bootstraptable.git", "_target": "~1.11.3", "_originalSource": "fastadmin-bootstraptable"}