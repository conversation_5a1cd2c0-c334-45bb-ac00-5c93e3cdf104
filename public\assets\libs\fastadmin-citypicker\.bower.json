{"name": "fastadmin-citypicker", "description": "A simple jQuery plugin for picking provinces, cities and districts of China.", "main": ["dist/js/city-picker.js"], "keywords": ["中国", "省份", "城市", "行政区", "省市区", "三级联动", "地址选择器", "China", "province", "provinces", "city", "cities", "district", "districts", "pick", "picker", "picking", "j<PERSON>y", "plugin", "html", "css", "javascript", "front-end", "web", "development"], "authors": ["Tao Shi"], "homepage": "https://github.com/karsonzhang/fastadmin-citypicker", "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "docs"], "dependencies": {"jquery": ">= 1.9.1"}, "version": "1.3.2", "_release": "1.3.2", "_resolution": {"type": "version", "tag": "v1.3.2", "commit": "f43906ccdc1fcd33612b18c487dd5387dd420a90"}, "_source": "https://github.com/karsonzhang/fastadmin-citypicker.git", "_target": "~1.3.1", "_originalSource": "fastadmin-citypicker"}