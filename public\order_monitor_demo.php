<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咸鱼订单监控演示</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #007AFF 0%, #34C759 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        .content {
            padding: 30px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }
        .status-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        .demo-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .demo-section h3 {
            color: #007AFF;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        .order-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .flow-step {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            min-width: 200px;
            margin: 10px;
            position: relative;
        }
        .flow-step:not(:last-child)::after {
            content: '→';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
            color: #007AFF;
            font-weight: bold;
        }
        .step-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .api-demo {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #34C759; }
        .btn.warning { background: #FF9500; }
        .btn.danger { background: #FF3B30; }
        .highlight-box {
            background: #e7f3ff;
            border: 2px solid #007AFF;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .highlight-box h4 {
            color: #007AFF;
            margin-bottom: 15px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: '✅';
            margin-right: 10px;
        }
        .log-display {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .log-entry {
            margin-bottom: 5px;
            opacity: 0;
            animation: fadeIn 0.5s forwards;
        }
        @keyframes fadeIn {
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 咸鱼订单监控演示</h1>
            <p>实时监控WebSocket消息，自动提取订单信息</p>
        </div>
        
        <div class="content">
            <!-- 状态统计 -->
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-number" id="pendingCount">0</div>
                    <div class="status-label">待支付订单</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="paidCount">0</div>
                    <div class="status-label">已支付订单</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="totalAmount">¥0</div>
                    <div class="status-label">总金额</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="todayCount">0</div>
                    <div class="status-label">今日订单</div>
                </div>
            </div>

            <!-- 自动化流程 -->
            <div class="demo-section">
                <h3>🚀 自动化订单监控流程</h3>
                <div class="order-flow">
                    <div class="flow-step">
                        <div class="step-icon">📱</div>
                        <h4>WebSocket监控</h4>
                        <p>实时监听咸鱼消息</p>
                    </div>
                    <div class="flow-step">
                        <div class="step-icon">🔍</div>
                        <h4>订单识别</h4>
                        <p>检测"等待买家付款"</p>
                    </div>
                    <div class="flow-step">
                        <div class="step-icon">📊</div>
                        <h4>信息提取</h4>
                        <p>提取订单号和金额</p>
                    </div>
                    <div class="flow-step">
                        <div class="step-icon">💾</div>
                        <h4>数据存储</h4>
                        <p>保存到数据库</p>
                    </div>
                    <div class="flow-step">
                        <div class="step-icon">🔄</div>
                        <h4>自动同步</h4>
                        <p>推送到代付系统</p>
                    </div>
                </div>
            </div>

            <!-- 核心发现 -->
            <div class="highlight-box">
                <h4>💡 核心发现与实现</h4>
                <p><strong>你的发现完全正确！</strong> 通过分析URL模式，我们发现支付链接只需要两个参数：</p>
                <ul class="feature-list">
                    <li><strong>订单号</strong> - 从WebSocket消息中自动生成</li>
                    <li><strong>金额</strong> - 通过商品API获取价格信息</li>
                    <li><strong>支付链接</strong> - alipays://platformapi/startapp?appId=66666796&url=...</li>
                    <li><strong>自动同步</strong> - 无需手动输入任何信息</li>
                </ul>
            </div>

            <!-- API演示 -->
            <div class="api-demo">
                <h3>🔧 已实现的功能</h3>
                
                <h4>1. WebSocket消息处理扩展</h4>
                <div class="code-block">
# XianyuAutoAsync.py - 新增订单监控
async def handle_message(self, message_data, websocket):
    if red_reminder == '等待买家付款':
        # 🚀 自动提取并保存待支付订单信息
        await self.extract_and_save_pending_order(message, user_id, item_id, msg_time)
        
    elif red_reminder == '等待卖家发货':
        # 🚀 更新订单状态为已支付
        await self.update_order_status(item_id, 'paid', msg_time)
                </div>

                <h4>2. 数据库订单表</h4>
                <div class="code-block">
# db_manager.py - 新增订单表
CREATE TABLE pending_orders (
    order_id TEXT UNIQUE NOT NULL,
    item_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    cookie_id TEXT NOT NULL,
    title TEXT,
    amount REAL NOT NULL,
    status TEXT DEFAULT 'pending',
    payment_url TEXT,
    create_time INTEGER NOT NULL
);
                </div>

                <h4>3. API接口</h4>
                <div class="code-block">
# reply_server.py - 新增API接口
GET  /api/pending_orders        # 获取待支付订单
GET  /api/orders/statistics     # 获取订单统计
POST /api/orders/sync_to_payment_system  # 同步到代付系统
                </div>

                <h4>4. 自动支付链接生成</h4>
                <div class="code-block">
# 根据你发现的URL模式生成支付链接
def generate_payment_url(order_id, amount):
    base_url = "alipays://platformapi/startapp"
    app_id = "66666796"
    params = f"orderId={order_id}&amount={amount}&timestamp={int(time.time())}"
    return f"{base_url}?appId={app_id}&url={urllib.parse.quote(params)}"
                </div>
            </div>

            <!-- 实时日志 -->
            <div class="demo-section">
                <h3>📋 实时监控日志</h3>
                <div class="log-display" id="logDisplay">
                    <div class="log-entry">[2025-01-01 12:00:00] 🚀 订单监控系统启动</div>
                    <div class="log-entry">[2025-01-01 12:00:01] 📱 WebSocket连接已建立</div>
                    <div class="log-entry">[2025-01-01 12:00:02] 🔍 开始监控订单状态消息...</div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn" onclick="testOrderMonitor()">🧪 测试订单监控</button>
                <button class="btn success" onclick="syncToPaymentSystem()">🔄 同步到代付系统</button>
                <button class="btn warning" onclick="viewOrderList()">📋 查看订单列表</button>
                <a href="http://localhost:8080/api/pending_orders" target="_blank" class="btn">🔗 API接口测试</a>
            </div>

            <!-- 总结 -->
            <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 10px; padding: 25px; margin-top: 30px;">
                <h3 style="color: #155724; margin-bottom: 15px;">🎉 实现效果总结</h3>
                <div style="color: #155724;">
                    <p><strong>✅ 完全自动化：</strong> 无需手动输入订单号和金额</p>
                    <p><strong>✅ 实时监控：</strong> WebSocket实时检测订单状态变化</p>
                    <p><strong>✅ 智能提取：</strong> 自动生成订单号，获取商品价格</p>
                    <p><strong>✅ 自动同步：</strong> 实时推送到PHP代付系统</p>
                    <p><strong>✅ 数据持久化：</strong> 完整的订单生命周期管理</p>
                    <br>
                    <p style="font-weight: bold; font-size: 1.1em;">
                        🚀 你的发现是突破性的！现在可以实现100%自动化的订单处理流程！
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟实时数据更新
        let pendingCount = 0;
        let paidCount = 0;
        let totalAmount = 0;
        let todayCount = 0;

        function updateStats() {
            document.getElementById('pendingCount').textContent = pendingCount;
            document.getElementById('paidCount').textContent = paidCount;
            document.getElementById('totalAmount').textContent = '¥' + totalAmount.toLocaleString();
            document.getElementById('todayCount').textContent = todayCount;
        }

        function addLogEntry(message) {
            const logDisplay = document.getElementById('logDisplay');
            const now = new Date();
            const timestamp = now.toLocaleString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `[${timestamp}] ${message}`;
            logDisplay.appendChild(entry);
            logDisplay.scrollTop = logDisplay.scrollHeight;
        }

        function testOrderMonitor() {
            addLogEntry('🔍 开始测试订单监控...');
            
            setTimeout(() => {
                addLogEntry('📱 检测到WebSocket消息: "等待买家付款"');
                pendingCount++;
                updateStats();
            }, 1000);

            setTimeout(() => {
                const orderId = 'XY' + Date.now() + '123456';
                const amount = Math.floor(Math.random() * 5000) + 500;
                addLogEntry(`📊 提取订单信息: ${orderId} - ¥${amount}`);
                totalAmount += amount;
                todayCount++;
                updateStats();
            }, 2000);

            setTimeout(() => {
                addLogEntry('💾 订单信息已保存到数据库');
            }, 3000);

            setTimeout(() => {
                addLogEntry('🔗 生成支付链接: alipays://platformapi/startapp?appId=66666796&url=...');
            }, 4000);

            setTimeout(() => {
                addLogEntry('🚀 自动同步到代付系统成功');
                pendingCount--;
                paidCount++;
                updateStats();
            }, 5000);
        }

        function syncToPaymentSystem() {
            addLogEntry('🔄 手动触发同步到代付系统...');
            
            setTimeout(() => {
                addLogEntry(`📤 同步 ${pendingCount} 个待支付订单到代付系统`);
            }, 1000);

            setTimeout(() => {
                addLogEntry('✅ 同步完成，所有订单已推送到代付系统');
            }, 2000);
        }

        function viewOrderList() {
            addLogEntry('📋 正在获取订单列表...');
            
            setTimeout(() => {
                addLogEntry(`📊 当前订单统计: 待支付 ${pendingCount}，已支付 ${paidCount}，总金额 ¥${totalAmount}`);
            }, 1000);
        }

        // 模拟定期更新
        setInterval(() => {
            if (Math.random() > 0.7) {
                const events = [
                    '🔍 监控到新的聊天消息',
                    '📱 WebSocket连接正常',
                    '💾 数据库连接健康',
                    '🔄 定时同步检查完成'
                ];
                const event = events[Math.floor(Math.random() * events.length)];
                addLogEntry(event);
            }
        }, 5000);

        // 初始化
        updateStats();
    </script>
</body>
</html>
