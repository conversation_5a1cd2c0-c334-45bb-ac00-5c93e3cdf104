<?php
/**
 * FastAdmin 密码管理工具
 * 用于生成和验证密码
 */

// 数据库配置 - 请根据你的实际配置修改
$dbConfig = array(
    'host' => '127.0.0.1',
    'dbname' => 'your_database_name',  // 替换为你的数据库名
    'username' => 'your_db_username',  // 替换为你的数据库用户名
    'password' => 'your_db_password'   // 替换为你的数据库密码
);

// 密码加密函数（与系统一致）
function encryptPassword($password, $salt) {
    return md5(md5($password) . $salt);
}

// 生成随机盐值
function generateSalt($length = 6) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    $salt = '';
    for ($i = 0; $i < $length; $i++) {
        $salt .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $salt;
}

echo "<h2>FastAdmin 密码管理工具</h2>";

// 如果是POST请求，处理密码更新
if ($_POST) {
    $action = $_POST['action'] ?? '';
    $newPassword = $_POST['new_password'] ?? '123456';
    
    try {
        $pdo = new PDO("mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']}", 
                      $dbConfig['username'], $dbConfig['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        if ($action === 'update_password') {
            // 生成新的盐值和密码
            $newSalt = generateSalt();
            $encryptedPassword = encryptPassword($newPassword, $newSalt);
            
            $stmt = $pdo->prepare("UPDATE df_admin SET password = ?, salt = ? WHERE username = 'admin'");
            $result = $stmt->execute([$encryptedPassword, $newSalt]);
            
            if ($result) {
                echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
                echo "<strong>密码更新成功！</strong><br>";
                echo "用户名: admin<br>";
                echo "新密码: {$newPassword}<br>";
                echo "新盐值: {$newSalt}<br>";
                echo "加密后: {$encryptedPassword}<br>";
                echo "</div>";
            } else {
                echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
                echo "密码更新失败！";
                echo "</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "数据库连接失败: " . $e->getMessage();
        echo "<br><strong>请检查上面的数据库配置是否正确</strong>";
        echo "</div>";
    }
}

// 显示当前数据库中的管理员信息
try {
    $pdo = new PDO("mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']}", 
                  $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SELECT username, password, salt FROM df_admin WHERE username = 'admin'");
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<h3>当前管理员信息</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><td>用户名</td><td>{$admin['username']}</td></tr>";
        echo "<tr><td>密码(加密)</td><td>{$admin['password']}</td></tr>";
        echo "<tr><td>盐值</td><td>{$admin['salt']}</td></tr>";
        echo "</table>";
        
        // 验证常见密码
        echo "<h3>密码验证</h3>";
        $commonPasswords = ['123456', 'admin', 'password', 'admin123', '123456789', 'admin123456'];
        $found = false;
        foreach ($commonPasswords as $testPassword) {
            $testEncrypted = encryptPassword($testPassword, $admin['salt']);
            if ($testEncrypted === $admin['password']) {
                echo "<div style='color: green;'><strong>当前密码是: {$testPassword}</strong></div>";
                $found = true;
                break;
            }
        }
        if (!$found) {
            echo "<div style='color: orange;'>未找到匹配的常见密码</div>";
        }
    } else {
        echo "<div style='color: red;'>未找到admin用户</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
    echo "数据库连接失败: " . $e->getMessage();
    echo "<br><strong>请修改文件顶部的数据库配置</strong>";
    echo "</div>";
}
?>

<h3>更新密码</h3>
<form method="post">
    <input type="hidden" name="action" value="update_password">
    <label>新密码: </label>
    <input type="text" name="new_password" value="123456" style="padding: 5px;">
    <button type="submit" style="padding: 5px 10px; background: #007cba; color: white; border: none;">更新密码</button>
</form>

<h3>手动SQL语句</h3>
<p>如果上面的自动更新不工作，可以手动执行以下SQL：</p>
<?php
$manualSalt = generateSalt();
$manualPassword = encryptPassword('123456', $manualSalt);
echo "<code style='background: #f5f5f5; padding: 10px; display: block;'>";
echo "UPDATE df_admin SET password = '{$manualPassword}', salt = '{$manualSalt}' WHERE username = 'admin';";
echo "</code>";
?>

<hr>
<p><strong>使用说明：</strong></p>
<ol>
    <li>首先修改文件顶部的数据库配置</li>
    <li>刷新页面查看当前管理员信息</li>
    <li>点击"更新密码"按钮自动更新</li>
    <li>或者复制手动SQL语句到数据库执行</li>
    <li>完成后删除此文件</li>
</ol>
