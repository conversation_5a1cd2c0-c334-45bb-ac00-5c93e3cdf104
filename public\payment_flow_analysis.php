<?php
/**
 * 咸鱼代付系统支付流程详细分析
 * 分析下单支付逻辑和所需参数
 */

echo "<h1>🔍 咸鱼代付系统支付流程分析</h1>";

// 支付流程步骤
function displayPaymentFlow() {
    echo "<h2>💳 支付流程步骤</h2>";
    
    $steps = [
        [
            'step' => 1,
            'title' => '第三方商户下单',
            'description' => '商户通过API向代付系统发起下单请求',
            'endpoint' => 'POST /api/order',
            'params' => [
                'money' => '订单金额 (必填)',
                'part_sn' => '商户订单号 (必填)',
                'notify' => '异步回调地址 (必填)',
                'id' => '码商ID (必填)',
                'sign' => 'MD5签名 (必填)'
            ],
            'example' => [
                'money' => '30.00',
                'part_sn' => 'ORDER_20250101_001',
                'notify' => 'https://merchant.com/callback',
                'id' => '2',
                'sign' => 'calculated_md5_hash'
            ]
        ],
        [
            'step' => 2,
            'title' => '系统分配咸鱼订单',
            'description' => '从df_product表中选择可用的咸鱼订单',
            'process' => [
                '查询条件：is_sale=1, is_lock=0, is_expire=1, sum=订单金额',
                '锁定订单：设置is_lock=1',
                '生成系统订单号：格式如20200330171822101995',
                '插入df_order表记录订单信息',
                '设置订单过期时间（默认15分钟）'
            ]
        ],
        [
            'step' => 3,
            'title' => '返回支付页面',
            'description' => '返回H5支付页面链接给商户',
            'response' => [
                'code' => 1,
                'msg' => '获取成功！',
                'data' => [
                    'order_sn' => '系统订单号',
                    'h5_url' => '支付页面链接'
                ]
            ]
        ],
        [
            'step' => 4,
            'title' => '用户访问支付页面',
            'description' => '用户通过H5页面扫码或跳转支付',
            'url_format' => '/index.php/index/qr?order_sn={order_sn}',
            'features' => [
                '自动识别设备类型（PC/Android/iOS）',
                '显示对应的支付界面',
                '解析二维码内容或直接跳转支付链接'
            ]
        ],
        [
            'step' => 5,
            'title' => '支付完成回调',
            'description' => '手动确认支付后触发回调通知',
            'callback_params' => [
                'callbacks' => 'CODE_SUCCESS',
                'total' => '订单金额',
                'paytime' => '支付时间戳',
                'order_sn' => '商户订单号(part_sn)',
                'sign' => 'MD5签名'
            ]
        ]
    ];
    
    foreach ($steps as $step) {
        echo "<div style='border: 2px solid #2196F3; border-radius: 10px; margin: 20px 0; padding: 20px; background: #f8f9fa;'>";
        echo "<h3 style='color: #2196F3; margin: 0 0 10px 0;'>步骤 {$step['step']}: {$step['title']}</h3>";
        echo "<p style='color: #666; margin: 10px 0;'>{$step['description']}</p>";
        
        if (isset($step['endpoint'])) {
            echo "<p><strong>API端点:</strong> <code>{$step['endpoint']}</code></p>";
        }
        
        if (isset($step['params'])) {
            echo "<h4>请求参数:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #e3f2fd;'><th>参数名</th><th>说明</th><th>示例值</th></tr>";
            foreach ($step['params'] as $param => $desc) {
                $example = isset($step['example'][$param]) ? $step['example'][$param] : '-';
                echo "<tr><td><code>{$param}</code></td><td>{$desc}</td><td><code>{$example}</code></td></tr>";
            }
            echo "</table>";
        }
        
        if (isset($step['process'])) {
            echo "<h4>处理流程:</h4>";
            echo "<ul>";
            foreach ($step['process'] as $process) {
                echo "<li>{$process}</li>";
            }
            echo "</ul>";
        }
        
        if (isset($step['response'])) {
            echo "<h4>返回示例:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
            echo json_encode($step['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            echo "</pre>";
        }
        
        if (isset($step['features'])) {
            echo "<h4>功能特性:</h4>";
            echo "<ul>";
            foreach ($step['features'] as $feature) {
                echo "<li>{$feature}</li>";
            }
            echo "</ul>";
        }
        
        if (isset($step['callback_params'])) {
            echo "<h4>回调参数:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #e8f5e8;'><th>参数名</th><th>说明</th></tr>";
            foreach ($step['callback_params'] as $param => $desc) {
                echo "<tr><td><code>{$param}</code></td><td>{$desc}</td></tr>";
            }
            echo "</table>";
        }
        
        echo "</div>";
    }
}

// 关键参数详解
function displayKeyParameters() {
    echo "<h2>🔑 关键参数详解</h2>";
    
    $categories = [
        '下单必需参数' => [
            'money' => [
                'type' => 'decimal(10,2)',
                'required' => true,
                'description' => '订单金额，必须与df_product表中的sum字段匹配',
                'validation' => '大于0，最多2位小数',
                'example' => '30.00, 99.99, 1000.00'
            ],
            'part_sn' => [
                'type' => 'string',
                'required' => true,
                'description' => '商户平台的订单号，用于回调时识别订单',
                'validation' => '唯一性，建议包含时间戳',
                'example' => 'ORDER_20250101_001, PAY_1735689600_123'
            ],
            'notify' => [
                'type' => 'url',
                'required' => true,
                'description' => '支付成功后的异步回调地址',
                'validation' => '必须是有效的HTTP/HTTPS URL',
                'example' => 'https://merchant.com/payment/callback'
            ],
            'id' => [
                'type' => 'integer',
                'required' => true,
                'description' => '码商ID，对应df_admin表中的管理员ID',
                'validation' => '必须存在于df_admin表',
                'example' => '1, 2, 3'
            ],
            'sign' => [
                'type' => 'string(32)',
                'required' => true,
                'description' => 'MD5签名，用于验证请求合法性',
                'algorithm' => 'MD5(app_secret + key1value1key2value2... + app_secret)',
                'example' => 'a1b2c3d4e5f67890*********01234567'
            ]
        ],
        '系统内部参数' => [
            'order_sn' => [
                'type' => 'string',
                'required' => false,
                'description' => '系统生成的订单号，格式：年月日时分秒+随机数',
                'generation' => 'date("YmdHis") + random_number',
                'example' => '20200330171822101995'
            ],
            'expire_time' => [
                'type' => 'timestamp',
                'required' => false,
                'description' => '订单过期时间，默认15分钟',
                'calculation' => 'time() + 900',
                'example' => '1735690500'
            ],
            'tb_order_sn' => [
                'type' => 'string',
                'required' => false,
                'description' => '咸鱼订单号，来自df_product表',
                'source' => 'df_product.tb_order_sn',
                'example' => '******************'
            ]
        ],
        '支付页面参数' => [
            'tb_qr_url' => [
                'type' => 'string',
                'required' => false,
                'description' => '咸鱼支付链接，优先使用URL而非二维码图片',
                'format' => 'alipays://platformapi/startapp?appId=xxx&url=xxx',
                'example' => 'alipays://platformapi/startapp?appId=********&url=...'
            ],
            'tb_qr' => [
                'type' => 'string',
                'required' => false,
                'description' => '二维码图片路径，当tb_qr_url为空时使用',
                'fallback' => '通过PHPZxing解析二维码内容',
                'example' => '/uploads/qr/20200330_123456.png'
            ]
        ],
        '回调参数' => [
            'callbacks' => [
                'type' => 'string',
                'required' => true,
                'description' => '回调状态码，固定值CODE_SUCCESS',
                'value' => 'CODE_SUCCESS',
                'example' => 'CODE_SUCCESS'
            ],
            'total' => [
                'type' => 'decimal',
                'required' => true,
                'description' => '实际支付金额',
                'source' => 'df_order.order_sum',
                'example' => '30.00'
            ],
            'paytime' => [
                'type' => 'timestamp',
                'required' => true,
                'description' => '支付完成时间',
                'value' => 'time()',
                'example' => '1735689600'
            ]
        ]
    ];
    
    foreach ($categories as $category => $params) {
        echo "<h3 style='color: #4CAF50; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;'>{$category}</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
        echo "<tr style='background: #f0f8ff;'>";
        echo "<th>参数名</th><th>类型</th><th>必填</th><th>说明</th><th>示例/规则</th>";
        echo "</tr>";
        
        foreach ($params as $param => $info) {
            $required = $info['required'] ? '✅ 是' : '❌ 否';
            $requiredColor = $info['required'] ? 'color: green;' : 'color: orange;';
            
            echo "<tr>";
            echo "<td><strong><code>{$param}</code></strong></td>";
            echo "<td><code>{$info['type']}</code></td>";
            echo "<td style='{$requiredColor}'>{$required}</td>";
            echo "<td>{$info['description']}</td>";
            echo "<td>";
            
            if (isset($info['example'])) {
                echo "<strong>示例:</strong> <code>{$info['example']}</code>";
            }
            if (isset($info['validation'])) {
                echo "<br><strong>验证:</strong> {$info['validation']}";
            }
            if (isset($info['algorithm'])) {
                echo "<br><strong>算法:</strong> <code>{$info['algorithm']}</code>";
            }
            if (isset($info['generation'])) {
                echo "<br><strong>生成:</strong> {$info['generation']}";
            }
            if (isset($info['source'])) {
                echo "<br><strong>来源:</strong> {$info['source']}";
            }
            if (isset($info['format'])) {
                echo "<br><strong>格式:</strong> {$info['format']}";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// 数据库表关系
function displayDatabaseRelations() {
    echo "<h2>🗄️ 数据库表关系</h2>";
    
    echo "<div style='background: #f5f5f5; padding: 20px; border-radius: 10px; font-family: monospace;'>";
    echo "<pre>";
    echo "┌─────────────────────────────────────────────────────────────────┐\n";
    echo "│                        数据库表关系图                            │\n";
    echo "└─────────────────────────────────────────────────────────────────┘\n";
    echo "\n";
    echo "┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐\n";
    echo "│   df_admin      │    │   df_product    │    │   df_order      │\n";
    echo "│                 │    │                 │    │                 │\n";
    echo "│ id (PK)         │◄──┤ admin_id (FK)   │    │ id (PK)         │\n";
    echo "│ username        │    │ tb_order_sn     │◄──┤ tb_order_sn     │\n";
    echo "│ app_secret      │    │ tb_qr_url       │    │ order_sn        │\n";
    echo "│                 │    │ sum             │    │ part_sn         │\n";
    echo "│                 │    │ is_sale         │    │ order_sum       │\n";
    echo "│                 │    │ is_lock         │    │ order_status    │\n";
    echo "│                 │    │ is_expire       │    │ callback_url    │\n";
    echo "└─────────────────┘    └─────────────────┘    │ expire_time     │\n";
    echo "                                              │ pay_time        │\n";
    echo "┌─────────────────┐                          └─────────────────┘\n";
    echo "│   df_shop       │                                             \n";
    echo "│                 │                                             \n";
    echo "│ id (PK)         │◄──┐                                        \n";
    echo "│ tb_name         │   │                                        \n";
    echo "│ shop_status     │   │                                        \n";
    echo "└─────────────────┘   │                                        \n";
    echo "                      │                                        \n";
    echo "              ┌───────┴─────────┐                              \n";
    echo "              │ shop_id (FK)    │                              \n";
    echo "              │ (在product和     │                              \n";
    echo "              │  order表中)     │                              \n";
    echo "              └─────────────────┘                              \n";
    echo "</pre>";
    echo "</div>";
    
    echo "<h3>表字段说明</h3>";
    
    $tables = [
        'df_admin' => [
            'description' => '管理员/码商表',
            'key_fields' => [
                'id' => '管理员ID，作为码商标识',
                'app_secret' => '用于API签名验证的密钥',
                'username' => '管理员用户名'
            ]
        ],
        'df_product' => [
            'description' => '咸鱼商品/订单池表',
            'key_fields' => [
                'tb_order_sn' => '咸鱼订单号，唯一标识',
                'tb_qr_url' => '支付链接，优先使用',
                'tb_qr' => '二维码图片路径，备用',
                'sum' => '订单金额，用于匹配',
                'is_sale' => '是否可售 (1:可用 0:不可用)',
                'is_lock' => '是否锁定 (0:空闲 1:已锁定)',
                'is_expire' => '是否过期 (1:未过期 0:已过期)'
            ]
        ],
        'df_order' => [
            'description' => '系统订单表',
            'key_fields' => [
                'order_sn' => '系统订单号',
                'part_sn' => '商户订单号',
                'tb_order_sn' => '关联的咸鱼订单号',
                'order_status' => '订单状态 (0:未支付 1:已支付 2:已过期)',
                'callback_url' => '回调地址',
                'expire_time' => '订单过期时间'
            ]
        ],
        'df_shop' => [
            'description' => '店铺管理表',
            'key_fields' => [
                'id' => '店铺ID',
                'tb_name' => '咸鱼店铺名称',
                'shop_status' => '店铺状态 (0:正常 1:禁用)'
            ]
        ]
    ];
    
    foreach ($tables as $table => $info) {
        echo "<h4 style='color: #2196F3;'>{$table} - {$info['description']}</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #e3f2fd;'><th>字段名</th><th>说明</th></tr>";
        foreach ($info['key_fields'] as $field => $desc) {
            echo "<tr><td><code>{$field}</code></td><td>{$desc}</td></tr>";
        }
        echo "</table>";
    }
}

// 签名算法详解
function displaySignatureAlgorithm() {
    echo "<h2>🔐 签名算法详解</h2>";

    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3 style='margin: 0 0 10px 0; color: #856404;'>⚠️ 签名算法说明</h3>";
    echo "<p>签名用于验证API请求的合法性，防止参数被篡改。</p>";
    echo "</div>";

    echo "<h3>算法步骤</h3>";
    echo "<ol style='line-height: 1.8;'>";
    echo "<li><strong>参数排序：</strong>将所有参数按键名进行字典序排序（除sign参数外）</li>";
    echo "<li><strong>拼接字符串：</strong>按 key1value1key2value2... 的格式拼接</li>";
    echo "<li><strong>加密钥：</strong>在字符串头尾都加上app_secret</li>";
    echo "<li><strong>MD5加密：</strong>对最终字符串进行MD5加密</li>";
    echo "</ol>";

    echo "<h3>PHP代码示例</h3>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 15px 0;'>";
    echo "<pre style='margin: 0; font-size: 13px;'><code>";
    echo htmlspecialchars('function generateSign($params, $app_secret) {
    // 1. 移除sign参数（如果存在）
    unset($params[\'sign\']);

    // 2. 按键名排序
    ksort($params);

    // 3. 拼接参数
    $str = \'\';
    foreach ($params as $key => $value) {
        $str .= ($key . $value);
    }

    // 4. 头尾加入app_secret
    $str = $app_secret . $str . $app_secret;

    // 5. MD5加密
    return md5($str);
}

// 使用示例
$params = [
    \'money\' => \'30.00\',
    \'part_sn\' => \'ORDER_20250101_001\',
    \'notify\' => \'https://merchant.com/callback\',
    \'id\' => \'2\'
];
$app_secret = \'19405ab7e62642595654ac2e18028582\';
$sign = generateSign($params, $app_secret);

// 最终请求参数
$params[\'sign\'] = $sign;');
    echo "</code></pre>";
    echo "</div>";

    echo "<h3>签名验证示例</h3>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 15px 0;'>";
    echo "<pre style='margin: 0; font-size: 13px;'><code>";
    echo htmlspecialchars('// 服务端验证签名
function verifySign($params, $app_secret) {
    $received_sign = $params[\'sign\'];
    $calculated_sign = generateSign($params, $app_secret);

    return $received_sign === $calculated_sign;
}

// 验证示例
if (verifySign($_POST, $app_secret)) {
    // 签名验证通过，处理业务逻辑
    echo "签名验证成功";
} else {
    // 签名验证失败
    echo "签名验证失败";
}');
    echo "</code></pre>";
    echo "</div>";
}

// 完整API调用示例
function displayApiExamples() {
    echo "<h2>📡 完整API调用示例</h2>";

    echo "<h3>1. 下单API调用</h3>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 15px 0;'>";
    echo "<pre style='margin: 0; font-size: 12px;'><code>";
    echo htmlspecialchars('<?php
// 下单API调用示例
$api_url = \'http://你的域名/api/order\';
$app_secret = \'19405ab7e62642595654ac2e18028582\';

// 订单参数
$order_data = [
    \'money\' => \'30.00\',                    // 订单金额
    \'part_sn\' => \'ORDER_\' . time(),        // 商户订单号
    \'notify\' => \'https://merchant.com/callback\', // 回调地址
    \'id\' => \'2\'                           // 码商ID
];

// 生成签名
$order_data[\'sign\'] = generateSign($order_data, $app_secret);

// 发送请求
$response = sendRequest($api_url, $order_data, \'POST\');
$result = json_decode($response, true);

if ($result[\'code\'] == 1) {
    // 下单成功
    $order_sn = $result[\'data\'][\'order_sn\'];
    $h5_url = $result[\'data\'][\'h5_url\'];

    echo "下单成功！";
    echo "系统订单号: " . $order_sn;
    echo "支付页面: " . $h5_url;

    // 跳转到支付页面
    header("Location: " . $h5_url);
} else {
    // 下单失败
    echo "下单失败: " . $result[\'msg\'];
}');
    echo "</code></pre>";
    echo "</div>";

    echo "<h3>2. 支付回调处理</h3>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px 0;'>";
    echo "<pre style='margin: 0; font-size: 12px;'><code>";
    echo htmlspecialchars('<?php
// 支付回调处理示例
$app_secret = \'19405ab7e62642595654ac2e18028582\';

// 接收回调参数
$callback_data = $_POST;

// 验证签名
if (!verifySign($callback_data, $app_secret)) {
    echo "fail"; // 签名验证失败
    exit;
}

// 提取回调参数
$callbacks = $callback_data[\'callbacks\'];    // CODE_SUCCESS
$total = $callback_data[\'total\'];            // 支付金额
$paytime = $callback_data[\'paytime\'];        // 支付时间
$order_sn = $callback_data[\'order_sn\'];      // 商户订单号

// 验证回调状态
if ($callbacks !== \'CODE_SUCCESS\') {
    echo "fail"; // 支付状态异常
    exit;
}

// 查询订单信息
$order = getOrderByPartSn($order_sn);
if (!$order) {
    echo "fail"; // 订单不存在
    exit;
}

// 验证金额
if (abs($total - $order[\'amount\']) > 0.01) {
    echo "fail"; // 金额不匹配
    exit;
}

// 更新订单状态
if (updateOrderStatus($order_sn, \'paid\', $paytime)) {
    // 处理业务逻辑（发货、积分等）
    processBusiness($order);

    echo "success"; // 回调处理成功
} else {
    echo "fail"; // 订单更新失败
}');
    echo "</code></pre>";
    echo "</div>";

    echo "<h3>3. 咸鱼订单同步示例</h3>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px 0;'>";
    echo "<pre style='margin: 0; font-size: 12px;'><code>";
    echo htmlspecialchars('<?php
// 从咸鱼自动回复系统同步订单
$sync_api_url = \'http://你的域名/api/auto_import_orders\';

// 模拟从咸鱼系统获取的订单数据
$xianyu_orders = [
    [
        \'tb_order_sn\' => \'******************\',
        \'tb_name\' => \'iPhone 15 Pro 256GB\',
        \'tb_qr_url\' => \'alipays://platformapi/startapp?appId=********&url=...\',
        \'sum\' => 6999.00,
        \'item_id\' => \'*********\',
        \'user_id\' => \'buyer_user_id\',
        \'cookie_id\' => \'account_001\',
        \'is_auto\' => 1,
        \'remark\' => \'自动监控订单 - 2025-01-01 12:00:00\'
    ]
];

// 同步数据格式
$sync_data = [
    \'orders\' => $xianyu_orders,
    \'sync_time\' => time(),
    \'source\' => \'xianyu_auto_monitor\'
];

// 发送同步请求
$response = sendRequest($sync_api_url, json_encode($sync_data), \'POST\', [
    \'Content-Type: application/json\'
]);

$result = json_decode($response, true);
if ($result[\'success\']) {
    echo "同步成功: " . $result[\'data\'][\'success_count\'] . " 个订单";
} else {
    echo "同步失败: " . $result[\'message\'];
}');
    echo "</code></pre>";
    echo "</div>";
}

// 常见问题和解决方案
function displayTroubleshooting() {
    echo "<h2>🔧 常见问题和解决方案</h2>";

    $issues = [
        [
            'problem' => '签名验证失败',
            'causes' => [
                '参数排序错误',
                'app_secret不正确',
                '参数值包含特殊字符未正确处理',
                '编码问题（UTF-8）'
            ],
            'solutions' => [
                '确保参数按字典序排序',
                '检查app_secret是否与数据库中一致',
                '对特殊字符进行正确编码',
                '确保字符串编码为UTF-8'
            ]
        ],
        [
            'problem' => '找不到可用的咸鱼订单',
            'causes' => [
                'df_product表中没有对应金额的订单',
                '订单已被锁定（is_lock=1）',
                '订单已过期（is_expire=0）',
                '订单已下架（is_sale=0）'
            ],
            'solutions' => [
                '添加对应金额的咸鱼订单到df_product表',
                '释放已锁定但未支付的订单',
                '更新过期订单的状态',
                '检查订单的上架状态'
            ]
        ],
        [
            'problem' => '支付页面无法显示',
            'causes' => [
                '订单已过期',
                'tb_qr_url和tb_qr都为空',
                '二维码解析失败',
                '设备类型识别错误'
            ],
            'solutions' => [
                '延长订单过期时间',
                '确保咸鱼订单有有效的支付链接或二维码',
                '检查PHPZxing库配置',
                '优化设备类型检测逻辑'
            ]
        ],
        [
            'problem' => '回调通知失败',
            'causes' => [
                '回调地址不可访问',
                '回调参数格式错误',
                '网络超时',
                '商户服务器返回非success'
            ],
            'solutions' => [
                '确保回调地址可以正常访问',
                '检查回调参数格式和签名',
                '增加重试机制',
                '商户服务器正确处理回调并返回success'
            ]
        ]
    ];

    foreach ($issues as $issue) {
        echo "<div style='border: 1px solid #dc3545; border-radius: 8px; margin: 15px 0; padding: 15px; background: #f8d7da;'>";
        echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ {$issue['problem']}</h4>";

        echo "<div style='display: flex; gap: 20px;'>";

        echo "<div style='flex: 1;'>";
        echo "<h5 style='color: #721c24;'>可能原因：</h5>";
        echo "<ul style='margin: 5px 0; padding-left: 20px;'>";
        foreach ($issue['causes'] as $cause) {
            echo "<li>{$cause}</li>";
        }
        echo "</ul>";
        echo "</div>";

        echo "<div style='flex: 1;'>";
        echo "<h5 style='color: #155724;'>解决方案：</h5>";
        echo "<ul style='margin: 5px 0; padding-left: 20px;'>";
        foreach ($issue['solutions'] as $solution) {
            echo "<li style='color: #155724;'>{$solution}</li>";
        }
        echo "</ul>";
        echo "</div>";

        echo "</div>";
        echo "</div>";
    }
}

// 主要内容显示
displayPaymentFlow();
displayKeyParameters();
displayDatabaseRelations();
displaySignatureAlgorithm();
displayApiExamples();
displayTroubleshooting();

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 10px;'>";
echo "<h3 style='margin: 0 0 15px 0; color: #155724;'>📋 总结</h3>";
echo "<p><strong>咸鱼代付系统的核心参数包括：</strong></p>";
echo "<ul style='margin: 10px 0; padding-left: 20px;'>";
echo "<li><strong>下单参数：</strong>money（金额）、part_sn（商户订单号）、notify（回调地址）、id（码商ID）、sign（签名）</li>";
echo "<li><strong>系统参数：</strong>order_sn（系统订单号）、tb_order_sn（咸鱼订单号）、expire_time（过期时间）</li>";
echo "<li><strong>支付参数：</strong>tb_qr_url（支付链接）、tb_qr（二维码图片）</li>";
echo "<li><strong>回调参数：</strong>callbacks（状态）、total（金额）、paytime（支付时间）、order_sn（商户订单号）</li>";
echo "</ul>";
echo "<p>系统通过这些参数实现了完整的代付流程，从下单到支付再到回调通知。</p>";
echo "</div>";
?>
