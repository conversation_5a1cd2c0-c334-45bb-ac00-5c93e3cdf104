<?php
// 简单的密码测试工具

echo "<h2>密码加密测试</h2>";

// 原始数据库中的信息
$dbPassword = 'b10ba36999f387e0e493ade2f490729a';
$dbSalt = '9OxEVC';

echo "<h3>原始数据库信息</h3>";
echo "数据库密码: {$dbPassword}<br>";
echo "数据库盐值: {$dbSalt}<br><br>";

// 测试不同的加密方式
function testEncryption($password, $salt) {
    $methods = array(
        'md5(md5(password) + salt)' => md5(md5($password) . $salt),
        'md5(password + salt)' => md5($password . $salt),
        'md5(salt + password)' => md5($salt . $password),
        'md5(salt + md5(password))' => md5($salt . md5($password)),
        'sha1(md5(password) + salt)' => sha1(md5($password) . $salt),
        'md5(password)' => md5($password),
        'sha1(password)' => sha1($password)
    );
    
    return $methods;
}

// 测试常见密码
$testPasswords = array('123456', 'admin', 'password', 'admin123', 'fastadmin', '888888');

echo "<h3>密码测试结果</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>密码</th><th>加密方式</th><th>结果</th><th>匹配</th></tr>";

$found = false;
$correctPassword = '';
$correctMethod = '';

foreach ($testPasswords as $testPassword) {
    $results = testEncryption($testPassword, $dbSalt);
    
    foreach ($results as $method => $encrypted) {
        $match = ($encrypted === $dbPassword);
        if ($match) {
            $found = true;
            $correctPassword = $testPassword;
            $correctMethod = $method;
        }
        
        echo "<tr>";
        echo "<td>{$testPassword}</td>";
        echo "<td>{$method}</td>";
        echo "<td style='font-family: monospace; font-size: 12px;'>{$encrypted}</td>";
        echo "<td style='color: " . ($match ? 'green' : 'red') . "; font-weight: bold;'>" . ($match ? '✓ 匹配!' : '') . "</td>";
        echo "</tr>";
    }
}

echo "</table>";

if ($found) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; margin: 20px 0;'>";
    echo "<h3>🎉 找到正确密码！</h3>";
    echo "<strong>原始密码:</strong> {$correctPassword}<br>";
    echo "<strong>加密方式:</strong> {$correctMethod}<br>";
    echo "</div>";
    
    // 生成新的123456密码
    echo "<h3>生成新密码 (123456)</h3>";
    if ($correctMethod === 'md5(md5(password) + salt)') {
        $newPassword = md5(md5('123456') . $dbSalt);
    } elseif ($correctMethod === 'md5(password + salt)') {
        $newPassword = md5('123456' . $dbSalt);
    } elseif ($correctMethod === 'md5(salt + password)') {
        $newPassword = md5($dbSalt . '123456');
    } elseif ($correctMethod === 'md5(salt + md5(password))') {
        $newPassword = md5($dbSalt . md5('123456'));
    } else {
        $newPassword = md5(md5('123456') . $dbSalt); // 默认方式
    }
    
    echo "<strong>新密码 (123456) 的加密结果:</strong> {$newPassword}<br>";
    echo "<strong>SQL更新语句:</strong><br>";
    echo "<code style='background: #f8f9fa; padding: 10px; display: block; margin: 10px 0;'>";
    echo "UPDATE df_admin SET password = '{$newPassword}' WHERE username = 'admin';";
    echo "</code>";
    
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; margin: 20px 0;'>";
    echo "<h3>❌ 未找到匹配的密码</h3>";
    echo "<p>原始密码可能不是常见密码，或者使用了其他加密方式。</p>";
    echo "</div>";
    
    // 提供重置方案
    echo "<h3>重置方案</h3>";
    echo "<p>直接重置为 admin/123456：</p>";
    $resetPassword = md5(md5('123456') . $dbSalt);
    echo "<code style='background: #f8f9fa; padding: 10px; display: block; margin: 10px 0;'>";
    echo "UPDATE df_admin SET password = '{$resetPassword}' WHERE username = 'admin';";
    echo "</code>";
}

echo "<hr>";
echo "<h3>手动验证</h3>";
echo "<p>如果你知道当前密码，可以在下面测试：</p>";
echo "<form method='post'>";
echo "<input type='text' name='test_password' placeholder='输入密码' style='padding: 5px;'>";
echo "<button type='submit' style='padding: 5px 10px;'>测试</button>";
echo "</form>";

if (isset($_POST['test_password'])) {
    $testPwd = $_POST['test_password'];
    $testResults = testEncryption($testPwd, $dbSalt);
    
    echo "<h4>测试结果:</h4>";
    foreach ($testResults as $method => $encrypted) {
        $match = ($encrypted === $dbPassword);
        echo "<div style='color: " . ($match ? 'green' : 'black') . ";'>";
        echo "{$method}: {$encrypted} " . ($match ? '✓ 匹配!' : '');
        echo "</div>";
    }
}
?>
