<?php
/**
 * 咸鱼代付系统API测试工具
 * 上传到服务器后直接访问此文件进行测试
 */

echo "<h1>🧪 咸鱼代付系统API测试工具</h1>";

// 配置信息
$config = [
    'api_url' => 'http://' . $_SERVER['HTTP_HOST'] . '/api/order',
    'auto_import_url' => 'http://' . $_SERVER['HTTP_HOST'] . '/api/auto_import_orders',
    'app_secret' => '19405ab7e62642595654ac2e18028582', // 默认密钥，请根据实际情况修改
    'admin_id' => '1' // 默认管理员ID
];

// 签名生成函数
function generateSign($params, $app_secret) {
    unset($params['sign']);
    ksort($params);
    $str = '';
    foreach ($params as $key => $value) {
        $str .= ($key . $value);
    }
    $str = $app_secret . $str . $app_secret;
    return md5($str);
}

// 发送HTTP请求
function sendRequest($url, $data, $method = 'POST', $headers = []) {
    $ch = curl_init();
    
    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if (is_array($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        } else {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            $headers[] = 'Content-Type: application/json';
        }
    }
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error
    ];
}

// 测试下单API
function testOrderAPI($config) {
    echo "<h2>📝 测试下单API</h2>";
    
    $orderData = [
        'money' => '30.00',
        'part_sn' => 'TEST_' . time(),
        'notify' => 'http://test.com/callback',
        'id' => $config['admin_id']
    ];
    
    // 生成签名
    $orderData['sign'] = generateSign($orderData, $config['app_secret']);
    
    echo "<h3>请求参数:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f8ff;'><th>参数名</th><th>参数值</th></tr>";
    foreach ($orderData as $key => $value) {
        echo "<tr><td><code>{$key}</code></td><td><code>{$value}</code></td></tr>";
    }
    echo "</table>";
    
    echo "<h3>发送请求...</h3>";
    $result = sendRequest($config['api_url'], $orderData);
    
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>HTTP状态码:</strong> {$result['http_code']}<br>";
    if ($result['error']) {
        echo "<strong style='color: red;'>错误:</strong> {$result['error']}<br>";
    }
    echo "<strong>响应内容:</strong><br>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
    
    if ($result['response']) {
        $jsonData = json_decode($result['response'], true);
        if ($jsonData) {
            echo json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        } else {
            echo htmlspecialchars($result['response']);
        }
    } else {
        echo "无响应内容";
    }
    echo "</pre>";
    echo "</div>";
    
    return $result;
}

// 测试自动导入API
function testAutoImportAPI($config) {
    echo "<h2>🔄 测试自动导入API</h2>";
    
    $importData = [
        'orders' => [
            [
                'tb_order_sn' => 'XY' . time() . '123456',
                'tb_name' => 'iPhone 15 Pro 256GB',
                'tb_qr_url' => 'alipays://platformapi/startapp?appId=********&url=test',
                'sum' => 6999.00,
                'item_id' => '*********',
                'user_id' => 'test_user_001',
                'cookie_id' => 'account_001',
                'is_auto' => 1,
                'remark' => '测试自动导入订单 - ' . date('Y-m-d H:i:s')
            ]
        ],
        'sync_time' => time(),
        'source' => 'api_test'
    ];
    
    echo "<h3>请求数据:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px;'>";
    echo json_encode($importData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "</pre>";
    
    echo "<h3>发送请求...</h3>";
    $result = sendRequest($config['auto_import_url'], json_encode($importData));
    
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>HTTP状态码:</strong> {$result['http_code']}<br>";
    if ($result['error']) {
        echo "<strong style='color: red;'>错误:</strong> {$result['error']}<br>";
    }
    echo "<strong>响应内容:</strong><br>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
    
    if ($result['response']) {
        $jsonData = json_decode($result['response'], true);
        if ($jsonData) {
            echo json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        } else {
            echo htmlspecialchars($result['response']);
        }
    } else {
        echo "无响应内容";
    }
    echo "</pre>";
    echo "</div>";
    
    return $result;
}

// 检查数据库连接
function checkDatabase() {
    echo "<h2>🗄️ 检查数据库连接</h2>";
    
    try {
        // 尝试包含ThinkPHP
        if (file_exists('../application/database.php')) {
            $dbConfig = include '../application/database.php';
            
            $dsn = "mysql:host={$dbConfig['hostname']};port={$dbConfig['hostport']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
            $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; color: #155724;'>";
            echo "✅ 数据库连接成功！<br>";
            echo "数据库: {$dbConfig['database']}<br>";
            echo "主机: {$dbConfig['hostname']}:{$dbConfig['hostport']}";
            echo "</div>";
            
            // 检查关键表
            $tables = ['df_admin', 'df_product', 'df_order'];
            echo "<h3>检查数据表:</h3>";
            foreach ($tables as $table) {
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
                    $count = $stmt->fetchColumn();
                    echo "<span style='color: green;'>✅ {$table} ({$count} 条记录)</span><br>";
                } catch (Exception $e) {
                    echo "<span style='color: red;'>❌ {$table} (表不存在或无权限)</span><br>";
                }
            }
            
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; color: #721c24;'>";
            echo "❌ 找不到数据库配置文件";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; color: #721c24;'>";
        echo "❌ 数据库连接失败: " . $e->getMessage();
        echo "</div>";
    }
}

// 显示系统信息
function showSystemInfo() {
    echo "<h2>ℹ️ 系统信息</h2>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f8ff;'><th>项目</th><th>值</th></tr>";
    echo "<tr><td>PHP版本</td><td>" . PHP_VERSION . "</td></tr>";
    echo "<tr><td>服务器软件</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</td></tr>";
    echo "<tr><td>文档根目录</td><td>" . $_SERVER['DOCUMENT_ROOT'] . "</td></tr>";
    echo "<tr><td>当前URL</td><td>http://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . "</td></tr>";
    echo "<tr><td>时区</td><td>" . date_default_timezone_get() . "</td></tr>";
    echo "<tr><td>当前时间</td><td>" . date('Y-m-d H:i:s') . "</td></tr>";
    
    // 检查必要的PHP扩展
    $extensions = ['curl', 'json', 'pdo_mysql', 'mbstring'];
    foreach ($extensions as $ext) {
        $status = extension_loaded($ext) ? '✅ 已安装' : '❌ 未安装';
        $color = extension_loaded($ext) ? 'green' : 'red';
        echo "<tr><td>{$ext} 扩展</td><td style='color: {$color};'>{$status}</td></tr>";
    }
    
    echo "</table>";
}

// 主程序
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3 style='margin: 0 0 10px 0;'>📋 测试说明</h3>";
echo "<p>此工具用于测试咸鱼代付系统的API接口。请确保：</p>";
echo "<ul>";
echo "<li>数据库已正确配置并导入了表结构</li>";
echo "<li>df_admin表中有管理员记录</li>";
echo "<li>df_product表中有可用的咸鱼订单</li>";
echo "<li>修改下方的app_secret为实际值</li>";
echo "</ul>";
echo "</div>";

// 配置表单
if ($_POST) {
    $config['app_secret'] = $_POST['app_secret'] ?? $config['app_secret'];
    $config['admin_id'] = $_POST['admin_id'] ?? $config['admin_id'];
}

echo "<form method='post' style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h3>🔧 配置参数</h3>";
echo "<table>";
echo "<tr><td>APP Secret:</td><td><input type='text' name='app_secret' value='{$config['app_secret']}' style='width: 300px;'></td></tr>";
echo "<tr><td>管理员ID:</td><td><input type='text' name='admin_id' value='{$config['admin_id']}' style='width: 100px;'></td></tr>";
echo "<tr><td colspan='2'><input type='submit' value='开始测试' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'></td></tr>";
echo "</table>";
echo "</form>";

if ($_POST) {
    showSystemInfo();
    checkDatabase();
    testOrderAPI($config);
    testAutoImportAPI($config);
    
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3 style='margin: 0 0 10px 0; color: #0c5460;'>🎯 测试完成</h3>";
    echo "<p>如果测试失败，请检查：</p>";
    echo "<ul>";
    echo "<li>数据库连接配置是否正确</li>";
    echo "<li>df_admin表中的app_secret是否匹配</li>";
    echo "<li>df_product表中是否有对应金额的可用订单</li>";
    echo "<li>服务器错误日志中的详细错误信息</li>";
    echo "</ul>";
    echo "</div>";
}
?>
