<?php
/**
 * 咸鱼API检测工具
 * 检测项目中发现的咸鱼相关API地址
 */

echo "<h2>咸鱼代付API检测工具</h2>";

// 从代码中发现的API地址
$apis = [
    '注释中的咸鱼API' => 'http://api.glcgbhcr.cn/pddb/tdapi.php',
    '阿奇索订单查询API' => 'http://gw.api.agiso.com/alds/Trade/TradeInfo',
    '演示站点' => 'https://xy.t0zs.com/ZLTchzxFfK.php',
    '淘宝下载地址' => 'http://download.alicdn.com/wireless/taobao4android/latest/702757.apk'
];

// 测试API连通性
function testApiConnection($url, $timeout = 10) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $start_time = microtime(true);
    $response = curl_exec($ch);
    $end_time = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    curl_close($ch);
    
    return [
        'success' => $response !== false && empty($error),
        'http_code' => $http_code,
        'response_time' => $response_time,
        'error' => $error,
        'response' => $response
    ];
}

// 测试咸鱼API
function testXianyuApi() {
    $url = 'http://api.glcgbhcr.cn/pddb/tdapi.php';
    
    // 测试不同的参数组合
    $test_params = [
        ['c' => 'getXianYuPayStr', 'pddname' => 'test', 'orderNo' => '20200411163216158659393646000'],
        ['action' => 'getPayUrl', 'amount' => '100'],
        ['method' => 'createOrder', 'money' => '50']
    ];
    
    $results = [];
    
    foreach ($test_params as $index => $params) {
        $query_string = http_build_query($params);
        $test_url = $url . '?' . $query_string;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; XianyuBot/1.0)');
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        $results[] = [
            'params' => $params,
            'url' => $test_url,
            'success' => $response !== false && empty($error),
            'http_code' => $http_code,
            'error' => $error,
            'response' => $response
        ];
    }
    
    return $results;
}

echo "<h3>基础连通性测试</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>API名称</th><th>地址</th><th>状态</th><th>HTTP状态码</th><th>响应时间</th><th>错误信息</th></tr>";

foreach ($apis as $name => $url) {
    $result = testApiConnection($url);
    
    $status_color = $result['success'] ? 'green' : 'red';
    $status_text = $result['success'] ? '✓ 可访问' : '✗ 无法访问';
    
    echo "<tr>";
    echo "<td>{$name}</td>";
    echo "<td style='font-family: monospace; font-size: 12px; word-break: break-all;'>{$url}</td>";
    echo "<td style='color: {$status_color}; font-weight: bold;'>{$status_text}</td>";
    echo "<td>{$result['http_code']}</td>";
    echo "<td>{$result['response_time']}ms</td>";
    echo "<td style='color: red;'>{$result['error']}</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>咸鱼API详细测试</h3>";
$xianyu_results = testXianyuApi();

foreach ($xianyu_results as $index => $result) {
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo "<h4>测试 " . ($index + 1) . "</h4>";
    echo "<strong>请求URL:</strong> <code style='word-break: break-all;'>{$result['url']}</code><br>";
    echo "<strong>参数:</strong> " . json_encode($result['params']) . "<br>";
    echo "<strong>状态:</strong> " . ($result['success'] ? '<span style="color: green;">成功</span>' : '<span style="color: red;">失败</span>') . "<br>";
    echo "<strong>HTTP状态码:</strong> {$result['http_code']}<br>";
    if ($result['error']) {
        echo "<strong>错误:</strong> <span style='color: red;'>{$result['error']}</span><br>";
    }
    echo "<strong>响应内容:</strong><br>";
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 150px; overflow: auto; font-size: 12px;'>";
    echo htmlspecialchars(substr($result['response'], 0, 1000));
    if (strlen($result['response']) > 1000) echo "\n... (内容过长，已截断)";
    echo "</pre>";
    echo "</div>";
}

echo "<h3>系统分析</h3>";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 10px 0;'>";
echo "<h4>🔍 发现的关键信息:</h4>";
echo "<ul>";
echo "<li><strong>二维码存储:</strong> 系统将咸鱼二维码存储在数据库 <code>df_product</code> 表中</li>";
echo "<li><strong>字段说明:</strong>";
echo "<ul>";
echo "<li><code>tb_qr</code> - 二维码图片路径</li>";
echo "<li><code>tb_qr_url</code> - 二维码对应的URL链接</li>";
echo "<li><code>tb_order_sn</code> - 闲鱼/淘宝订单号</li>";
echo "</ul></li>";
echo "<li><strong>支付跳转:</strong> 使用淘宝和支付宝的URL Scheme进行跳转</li>";
echo "<li><strong>API依赖:</strong> 系统依赖外部API获取订单状态</li>";
echo "</ul>";

echo "<h4>⚠️ 可能的问题:</h4>";
echo "<ul>";
echo "<li>注释中的API地址可能已经失效</li>";
echo "<li>需要有效的咸鱼店铺和商品来生成二维码</li>";
echo "<li>系统需要手动上传或通过其他方式获取咸鱼二维码</li>";
echo "<li>阿奇索API用于查询订单状态，如果失效会影响支付确认</li>";
echo "</ul>";

echo "<h4>🔧 建议:</h4>";
echo "<ul>";
echo "<li>检查后台是否有咸鱼商品和二维码数据</li>";
echo "<li>联系原开发者获取最新的API接口</li>";
echo "<li>考虑寻找替代的订单查询API</li>";
echo "<li>手动测试现有的咸鱼二维码是否还有效</li>";
echo "</ul>";
echo "</div>";

echo "<h3>数据库检查</h3>";
echo "<p>建议检查以下数据库表的数据:</p>";
echo "<code>";
echo "SELECT * FROM df_product LIMIT 10;<br>";
echo "SELECT * FROM df_shop WHERE shop_status = 1;<br>";
echo "SELECT * FROM df_order WHERE order_status = 0 ORDER BY ctime DESC LIMIT 10;";
echo "</code>";
?>
