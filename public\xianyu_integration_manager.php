<?php
/**
 * 咸鱼系统集成管理界面
 * 展示PHP代付系统与Python咸鱼系统的集成效果
 */

// 模拟API客户端
class XianyuApiDemo {
    public function testConnection() {
        // 模拟连接测试
        return rand(0, 1) ? true : false;
    }
    
    public function getPendingOrders() {
        return [
            [
                'order_id' => 'XY202501010001',
                'title' => 'iPhone 15 Pro Max 256GB',
                'amount' => 8999,
                'status' => 'pending',
                'timestamp' => time() - 1800
            ],
            [
                'order_id' => 'XY202501010002', 
                'title' => 'MacBook Air M2 8GB+256GB',
                'amount' => 7999,
                'status' => 'pending',
                'timestamp' => time() - 3600
            ]
        ];
    }
    
    public function getAccountStatus() {
        return [
            [
                'id' => 'account_001',
                'shop_name' => '数码专营店',
                'status' => 'active',
                'last_check' => time() - 300,
                'orders_today' => 15
            ],
            [
                'id' => 'account_002',
                'shop_name' => '电脑配件店',
                'status' => 'expired', 
                'last_check' => time() - 86400,
                'orders_today' => 0
            ]
        ];
    }
    
    public function searchItems($keyword) {
        $items = [];
        for ($i = 1; $i <= 5; $i++) {
            $price = 1000 + ($i * 500);
            $items[] = [
                'item_id' => "XY{$keyword}{$i:06d}",
                'title' => "{$keyword}相关商品 #{$i}",
                'price' => "¥{$price}",
                'seller_name' => "卖家{$i}",
                'item_url' => "https://www.goofish.com/item?id=XY{$keyword}{$i:06d}",
                'want_count' => 100 - $i * 10
            ];
        }
        return $items;
    }
}

$xianyuApi = new XianyuApiDemo();
$action = $_GET['action'] ?? 'dashboard';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咸鱼系统集成管理</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #007AFF 0%, #34C759 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        .nav {
            background: #f8f9fa;
            padding: 0;
            display: flex;
            border-bottom: 1px solid #e9ecef;
        }
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 15px;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            color: #495057;
            font-weight: 500;
        }
        .nav-item:hover, .nav-item.active {
            background: #007AFF;
            color: white;
        }
        .content {
            padding: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #007AFF;
        }
        .status-card.success { border-left-color: #34C759; }
        .status-card.warning { border-left-color: #FF9500; }
        .status-card.error { border-left-color: #FF3B30; }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        .card h3 {
            color: #007AFF;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        .btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #34C759; }
        .btn.warning { background: #FF9500; }
        .btn.danger { background: #FF3B30; }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-expired { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .search-box input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐟 咸鱼系统集成管理</h1>
            <p>PHP代付系统 ⚡ Python咸鱼系统</p>
        </div>
        
        <div class="nav">
            <a href="?action=dashboard" class="nav-item <?= $action == 'dashboard' ? 'active' : '' ?>">📊 仪表盘</a>
            <a href="?action=orders" class="nav-item <?= $action == 'orders' ? 'active' : '' ?>">📋 订单同步</a>
            <a href="?action=accounts" class="nav-item <?= $action == 'accounts' ? 'active' : '' ?>">👥 账号管理</a>
            <a href="?action=search" class="nav-item <?= $action == 'search' ? 'active' : '' ?>">🔍 商品搜索</a>
            <a href="?action=settings" class="nav-item <?= $action == 'settings' ? 'active' : '' ?>">⚙️ 系统设置</a>
        </div>
        
        <div class="content">
            <?php if ($action == 'dashboard'): ?>
                <!-- 仪表盘 -->
                <div class="status-card <?= $xianyuApi->testConnection() ? 'success' : 'error' ?>">
                    <h3>🔗 系统连接状态</h3>
                    <p>
                        Python咸鱼系统: 
                        <strong><?= $xianyuApi->testConnection() ? '✅ 已连接' : '❌ 连接失败' ?></strong>
                        (http://localhost:8080)
                    </p>
                </div>
                
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number">15</div>
                        <div class="stat-label">今日同步订单</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">2</div>
                        <div class="stat-label">活跃账号</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">128</div>
                        <div class="stat-label">商品库存</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">98.5%</div>
                        <div class="stat-label">系统可用性</div>
                    </div>
                </div>
                
                <div class="grid">
                    <div class="card">
                        <h3>📈 最近活动</h3>
                        <ul style="list-style: none; padding: 0;">
                            <li style="padding: 8px 0; border-bottom: 1px solid #eee;">
                                <span style="color: #34C759;">✓</span> 同步了3个待支付订单
                                <small style="color: #666; float: right;">2分钟前</small>
                            </li>
                            <li style="padding: 8px 0; border-bottom: 1px solid #eee;">
                                <span style="color: #007AFF;">ℹ</span> 账号account_001状态正常
                                <small style="color: #666; float: right;">5分钟前</small>
                            </li>
                            <li style="padding: 8px 0; border-bottom: 1px solid #eee;">
                                <span style="color: #FF9500;">⚠</span> 账号account_002需要更新Cookie
                                <small style="color: #666; float: right;">1小时前</small>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h3>🎯 快速操作</h3>
                        <div style="display: flex; flex-direction: column; gap: 10px;">
                            <button class="btn" onclick="syncOrders()">🔄 立即同步订单</button>
                            <button class="btn success" onclick="searchProducts()">🔍 搜索热门商品</button>
                            <button class="btn warning" onclick="checkAccounts()">👥 检查账号状态</button>
                            <button class="btn" onclick="exportData()">📊 导出数据报告</button>
                        </div>
                    </div>
                </div>
                
            <?php elseif ($action == 'orders'): ?>
                <!-- 订单同步 -->
                <div class="status-card">
                    <h3>📋 待支付订单同步</h3>
                    <p>从咸鱼系统自动获取待支付订单，同步到代付系统</p>
                </div>
                
                <div class="card">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                        <h3>待支付订单列表</h3>
                        <button class="btn" onclick="syncOrders()">🔄 立即同步</button>
                    </div>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>商品标题</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($xianyuApi->getPendingOrders() as $order): ?>
                            <tr>
                                <td><?= $order['order_id'] ?></td>
                                <td><?= $order['title'] ?></td>
                                <td>¥<?= number_format($order['amount']) ?></td>
                                <td><span class="status-badge status-pending">待支付</span></td>
                                <td><?= date('Y-m-d H:i:s', $order['timestamp']) ?></td>
                                <td>
                                    <button class="btn" onclick="importOrder('<?= $order['order_id'] ?>')">导入</button>
                                    <button class="btn warning" onclick="getQrCode('<?= $order['order_id'] ?>')">获取二维码</button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
            <?php elseif ($action == 'accounts'): ?>
                <!-- 账号管理 -->
                <div class="status-card">
                    <h3>👥 咸鱼账号管理</h3>
                    <p>管理多个咸鱼店铺账号的Cookie和状态监控</p>
                </div>
                
                <div class="card">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                        <h3>账号状态监控</h3>
                        <button class="btn" onclick="checkAccounts()">🔄 检查状态</button>
                    </div>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>账号ID</th>
                                <th>店铺名称</th>
                                <th>状态</th>
                                <th>最后检查</th>
                                <th>今日订单</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($xianyuApi->getAccountStatus() as $account): ?>
                            <tr>
                                <td><?= $account['id'] ?></td>
                                <td><?= $account['shop_name'] ?></td>
                                <td>
                                    <span class="status-badge status-<?= $account['status'] ?>">
                                        <?= $account['status'] == 'active' ? '✅ 正常' : '❌ 失效' ?>
                                    </span>
                                </td>
                                <td><?= date('Y-m-d H:i:s', $account['last_check']) ?></td>
                                <td><?= $account['orders_today'] ?></td>
                                <td>
                                    <?php if ($account['status'] == 'expired'): ?>
                                        <button class="btn danger" onclick="updateCookie('<?= $account['id'] ?>')">更新Cookie</button>
                                    <?php else: ?>
                                        <button class="btn" onclick="testAccount('<?= $account['id'] ?>')">测试连接</button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
            <?php elseif ($action == 'search'): ?>
                <!-- 商品搜索 -->
                <div class="status-card">
                    <h3>🔍 商品搜索与导入</h3>
                    <p>搜索咸鱼热门商品，自动导入到代付系统</p>
                </div>
                
                <div class="card">
                    <h3>商品搜索</h3>
                    <div class="search-box">
                        <input type="text" id="searchKeyword" placeholder="输入搜索关键词..." value="手机">
                        <button class="btn" onclick="searchProducts()">🔍 搜索</button>
                        <button class="btn success" onclick="importAll()">📥 批量导入</button>
                    </div>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>商品ID</th>
                                <th>标题</th>
                                <th>价格</th>
                                <th>卖家</th>
                                <th>想要人数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="searchResults">
                            <?php foreach ($xianyuApi->searchItems('手机') as $item): ?>
                            <tr>
                                <td><?= $item['item_id'] ?></td>
                                <td><?= $item['title'] ?></td>
                                <td><?= $item['price'] ?></td>
                                <td><?= $item['seller_name'] ?></td>
                                <td><?= $item['want_count'] ?>人想要</td>
                                <td>
                                    <button class="btn" onclick="importItem('<?= $item['item_id'] ?>')">导入</button>
                                    <a href="<?= $item['item_url'] ?>" target="_blank" class="btn warning">查看</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
            <?php else: ?>
                <!-- 系统设置 -->
                <div class="status-card">
                    <h3>⚙️ 系统设置</h3>
                    <p>配置咸鱼系统集成参数</p>
                </div>
                
                <div class="grid">
                    <div class="card">
                        <h3>API配置</h3>
                        <form>
                            <div style="margin-bottom: 15px;">
                                <label>Python系统地址:</label>
                                <input type="text" value="http://localhost:8080" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label>连接超时(秒):</label>
                                <input type="number" value="30" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <button type="button" class="btn">💾 保存配置</button>
                        </form>
                    </div>
                    
                    <div class="card">
                        <h3>同步设置</h3>
                        <form>
                            <div style="margin-bottom: 15px;">
                                <label>
                                    <input type="checkbox" checked> 自动同步待支付订单
                                </label>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label>
                                    <input type="checkbox" checked> 自动检查账号状态
                                </label>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label>同步间隔(分钟):</label>
                                <input type="number" value="5" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <button type="button" class="btn">💾 保存设置</button>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function syncOrders() {
            alert('正在同步订单...\n\n这将调用Python系统API获取最新的待支付订单');
        }
        
        function searchProducts() {
            const keyword = document.getElementById('searchKeyword')?.value || '手机';
            alert(`正在搜索商品: ${keyword}\n\n这将调用咸鱼搜索API获取商品信息`);
        }
        
        function checkAccounts() {
            alert('正在检查账号状态...\n\n这将验证所有咸鱼账号的Cookie有效性');
        }
        
        function importOrder(orderId) {
            alert(`导入订单: ${orderId}\n\n订单将被添加到代付系统的产品库中`);
        }
        
        function getQrCode(orderId) {
            alert(`获取二维码: ${orderId}\n\n将从咸鱼系统获取支付二维码`);
        }
        
        function importItem(itemId) {
            alert(`导入商品: ${itemId}\n\n商品将被添加到代付系统中`);
        }
        
        function updateCookie(accountId) {
            alert(`更新Cookie: ${accountId}\n\n请提供新的Cookie值来更新账号`);
        }
        
        function testAccount(accountId) {
            alert(`测试账号: ${accountId}\n\n正在验证账号连接状态...`);
        }
        
        function exportData() {
            alert('正在导出数据报告...\n\n将生成包含订单、账号状态的Excel报告');
        }
        
        function importAll() {
            alert('批量导入商品...\n\n将导入当前搜索结果中的所有商品');
        }
    </script>
</body>
</html>
