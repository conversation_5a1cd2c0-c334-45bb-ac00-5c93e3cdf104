<?php
/**
 * 咸鱼代付系统快速部署脚本
 * 上传后访问此文件进行一键配置
 */

echo "<h1>⚡ 咸鱼代付系统快速部署</h1>";

// 检查是否已经配置过
$setupCompleted = file_exists('setup_completed.flag');

if ($setupCompleted && !isset($_GET['force'])) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 8px; color: #155724;'>";
    echo "<h3>✅ 系统已配置完成</h3>";
    echo "<p>如需重新配置，请访问: <a href='?force=1'>强制重新配置</a></p>";
    echo "<p>测试API: <a href='test_api.php' target='_blank'>API测试工具</a></p>";
    echo "<p>管理后台: <a href='ZLTchzxFfK.php' target='_blank'>管理后台</a></p>";
    echo "</div>";
    exit;
}

// 配置步骤
$steps = [
    1 => '环境检查',
    2 => '数据库配置',
    3 => '初始化数据库',
    4 => '系统配置',
    5 => '完成部署'
];

$currentStep = isset($_POST['step']) ? (int)$_POST['step'] : 1;

echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 部署步骤</h3>";
echo "<div style='display: flex; gap: 10px;'>";
foreach ($steps as $num => $title) {
    $class = $num == $currentStep ? 'background: #007bff; color: white;' : 
             ($num < $currentStep ? 'background: #28a745; color: white;' : 'background: #f8f9fa; color: #666;');
    echo "<div style='padding: 8px 12px; border-radius: 5px; {$class}'>{$num}. {$title}</div>";
}
echo "</div>";
echo "</div>";

// 步骤1: 环境检查
if ($currentStep == 1) {
    echo "<h2>🔍 步骤1: 环境检查</h2>";
    
    $checks = [
        'PHP版本 >= 7.1' => version_compare(PHP_VERSION, '7.1.0', '>='),
        'curl扩展' => extension_loaded('curl'),
        'json扩展' => extension_loaded('json'),
        'pdo_mysql扩展' => extension_loaded('pdo_mysql'),
        'mbstring扩展' => extension_loaded('mbstring'),
        'runtime目录可写' => is_writable('../runtime') || is_writable('.'),
        'public目录可写' => is_writable('.'),
    ];
    
    $allPassed = true;
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f8ff;'><th>检查项目</th><th>状态</th></tr>";
    
    foreach ($checks as $item => $passed) {
        $status = $passed ? '<span style="color: green;">✅ 通过</span>' : '<span style="color: red;">❌ 失败</span>';
        echo "<tr><td>{$item}</td><td>{$status}</td></tr>";
        if (!$passed) $allPassed = false;
    }
    echo "</table>";
    
    if ($allPassed) {
        echo "<form method='post' style='margin: 20px 0;'>";
        echo "<input type='hidden' name='step' value='2'>";
        echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>下一步: 数据库配置</button>";
        echo "</form>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 8px; color: #721c24; margin: 15px 0;'>";
        echo "<h4>❌ 环境检查未通过</h4>";
        echo "<p>请联系服务器管理员解决上述问题后重新检查。</p>";
        echo "</div>";
    }
}

// 步骤2: 数据库配置
elseif ($currentStep == 2) {
    echo "<h2>🗄️ 步骤2: 数据库配置</h2>";
    
    if ($_POST && isset($_POST['db_host'])) {
        // 测试数据库连接
        try {
            $dsn = "mysql:host={$_POST['db_host']};port={$_POST['db_port']};charset=utf8mb4";
            $pdo = new PDO($dsn, $_POST['db_user'], $_POST['db_pass']);
            
            // 创建数据库（如果不存在）
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$_POST['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // 生成数据库配置文件
            $dbConfig = "<?php\nreturn [\n";
            $dbConfig .= "    'type'            => 'mysql',\n";
            $dbConfig .= "    'hostname'        => '{$_POST['db_host']}',\n";
            $dbConfig .= "    'database'        => '{$_POST['db_name']}',\n";
            $dbConfig .= "    'username'        => '{$_POST['db_user']}',\n";
            $dbConfig .= "    'password'        => '{$_POST['db_pass']}',\n";
            $dbConfig .= "    'hostport'        => '{$_POST['db_port']}',\n";
            $dbConfig .= "    'charset'         => 'utf8mb4',\n";
            $dbConfig .= "    'prefix'          => '',\n";
            $dbConfig .= "    'debug'           => true,\n";
            $dbConfig .= "    'deploy'          => 0,\n";
            $dbConfig .= "    'rw_separate'     => false,\n";
            $dbConfig .= "    'master_num'      => 1,\n";
            $dbConfig .= "    'slave_no'        => '',\n";
            $dbConfig .= "    'fields_strict'   => true,\n";
            $dbConfig .= "    'resultset_type'  => 'array',\n";
            $dbConfig .= "    'auto_timestamp'  => false,\n";
            $dbConfig .= "    'datetime_format' => 'Y-m-d H:i:s',\n";
            $dbConfig .= "    'sql_explain'     => false,\n";
            $dbConfig .= "];\n";
            
            file_put_contents('../application/database.php', $dbConfig);
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 8px; color: #155724;'>";
            echo "<h4>✅ 数据库连接成功</h4>";
            echo "<p>数据库配置文件已生成。</p>";
            echo "</div>";
            
            echo "<form method='post' style='margin: 20px 0;'>";
            echo "<input type='hidden' name='step' value='3'>";
            echo "<input type='hidden' name='db_host' value='{$_POST['db_host']}'>";
            echo "<input type='hidden' name='db_port' value='{$_POST['db_port']}'>";
            echo "<input type='hidden' name='db_name' value='{$_POST['db_name']}'>";
            echo "<input type='hidden' name='db_user' value='{$_POST['db_user']}'>";
            echo "<input type='hidden' name='db_pass' value='{$_POST['db_pass']}'>";
            echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>下一步: 初始化数据库</button>";
            echo "</form>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 8px; color: #721c24;'>";
            echo "<h4>❌ 数据库连接失败</h4>";
            echo "<p>错误信息: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    if (!$_POST || !isset($_POST['db_host']) || isset($e)) {
        echo "<form method='post' style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
        echo "<input type='hidden' name='step' value='2'>";
        echo "<table style='width: 100%;'>";
        echo "<tr><td>数据库主机:</td><td><input type='text' name='db_host' value='localhost' required style='width: 200px; padding: 5px;'></td></tr>";
        echo "<tr><td>数据库端口:</td><td><input type='text' name='db_port' value='3306' required style='width: 200px; padding: 5px;'></td></tr>";
        echo "<tr><td>数据库名称:</td><td><input type='text' name='db_name' value='xianyu_payment' required style='width: 200px; padding: 5px;'></td></tr>";
        echo "<tr><td>数据库用户名:</td><td><input type='text' name='db_user' required style='width: 200px; padding: 5px;'></td></tr>";
        echo "<tr><td>数据库密码:</td><td><input type='password' name='db_pass' style='width: 200px; padding: 5px;'></td></tr>";
        echo "<tr><td colspan='2' style='padding-top: 15px;'>";
        echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>测试连接</button>";
        echo "</td></tr>";
        echo "</table>";
        echo "</form>";
    }
}

// 步骤3: 初始化数据库
elseif ($currentStep == 3) {
    echo "<h2>🔧 步骤3: 初始化数据库</h2>";
    
    try {
        $dsn = "mysql:host={$_POST['db_host']};port={$_POST['db_port']};dbname={$_POST['db_name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $_POST['db_user'], $_POST['db_pass']);
        
        // 检查是否已有数据
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            // 执行SQL初始化
            if (file_exists('xydf.sql')) {
                $sql = file_get_contents('xydf.sql');
                $pdo->exec($sql);
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 8px; color: #155724;'>";
                echo "<h4>✅ 数据库初始化成功</h4>";
                echo "<p>已导入基础数据表结构。</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; color: #856404;'>";
                echo "<h4>⚠️ 未找到SQL文件</h4>";
                echo "<p>请手动导入xydf.sql文件到数据库。</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 8px; color: #0c5460;'>";
            echo "<h4>ℹ️ 数据库已存在数据</h4>";
            echo "<p>检测到数据库中已有 " . count($tables) . " 个表，跳过初始化。</p>";
            echo "</div>";
        }
        
        echo "<form method='post' style='margin: 20px 0;'>";
        echo "<input type='hidden' name='step' value='4'>";
        echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>下一步: 系统配置</button>";
        echo "</form>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 8px; color: #721c24;'>";
        echo "<h4>❌ 数据库初始化失败</h4>";
        echo "<p>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// 步骤4: 系统配置
elseif ($currentStep == 4) {
    echo "<h2>⚙️ 步骤4: 系统配置</h2>";
    
    // 创建必要的目录
    $dirs = ['../runtime/log', '../runtime/cache', '../runtime/temp', 'uploads'];
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    // 设置权限
    chmod('../runtime', 0777);
    chmod('uploads', 0777);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 8px; color: #155724;'>";
    echo "<h4>✅ 系统配置完成</h4>";
    echo "<ul>";
    echo "<li>创建了必要的目录</li>";
    echo "<li>设置了正确的权限</li>";
    echo "<li>系统已准备就绪</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<form method='post' style='margin: 20px 0;'>";
    echo "<input type='hidden' name='step' value='5'>";
    echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>完成部署</button>";
    echo "</form>";
}

// 步骤5: 完成部署
elseif ($currentStep == 5) {
    echo "<h2>🎉 步骤5: 部署完成</h2>";
    
    // 创建完成标记文件
    file_put_contents('setup_completed.flag', date('Y-m-d H:i:s'));
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; color: #155724;'>";
    echo "<h3>🎊 恭喜！系统部署完成</h3>";
    echo "<p>咸鱼代付系统已成功部署，您现在可以：</p>";
    echo "<ul>";
    echo "<li><a href='ZLTchzxFfK.php' target='_blank' style='color: #007bff;'>访问管理后台</a> (用户名: admin)</li>";
    echo "<li><a href='test_api.php' target='_blank' style='color: #007bff;'>测试API接口</a></li>";
    echo "<li><a href='payment_flow_analysis.php' target='_blank' style='color: #007bff;'>查看支付流程分析</a></li>";
    echo "<li><a href='xianyu_system_integration.php' target='_blank' style='color: #007bff;'>查看系统集成方案</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; color: #856404; margin: 15px 0;'>";
    echo "<h4>📝 下一步操作建议</h4>";
    echo "<ol>";
    echo "<li>登录管理后台，修改默认密码</li>";
    echo "<li>添加咸鱼订单到df_product表</li>";
    echo "<li>配置app_secret密钥</li>";
    echo "<li>部署Python咸鱼自动回复系统</li>";
    echo "<li>测试完整的支付流程</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 8px; color: #721c24; margin: 15px 0;'>";
    echo "<h4>⚠️ 安全提醒</h4>";
    echo "<p>部署完成后，请删除此配置文件 (quick_setup.php) 以确保系统安全。</p>";
    echo "</div>";
}
?>
