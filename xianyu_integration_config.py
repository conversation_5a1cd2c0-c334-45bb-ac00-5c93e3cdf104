#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
咸鱼自动回复系统与代付系统集成配置
用于修改XianyuAutoAsync.py，添加订单同步功能
"""

import asyncio
import aiohttp
import json
import time
import logging

class PaymentSystemIntegration:
    """代付系统集成类"""
    
    def __init__(self, api_url="http://localhost/api/auto_import_orders", timeout=10):
        """
        初始化集成配置
        
        Args:
            api_url: PHP代付系统API地址
            timeout: 请求超时时间
        """
        self.api_url = api_url
        self.timeout = timeout
        self.retry_times = 3
        self.retry_delay = 1
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    async def send_order_to_payment_system(self, order_data):
        """
        发送订单数据到代付系统
        
        Args:
            order_data: 订单数据字典
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 包装为API要求的格式
            request_data = {
                'orders': [order_data],
                'sync_time': int(time.time()),
                'source': 'xianyu_auto_monitor'
            }
            
            # 发送请求
            success = await self._send_with_retry(request_data)
            
            if success:
                self.logger.info(f"订单同步成功: {order_data.get('tb_order_sn', 'unknown')}")
            else:
                self.logger.error(f"订单同步失败: {order_data.get('tb_order_sn', 'unknown')}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"发送订单到代付系统异常: {str(e)}")
            return False
    
    async def _send_with_retry(self, request_data):
        """
        带重试机制的发送方法
        
        Args:
            request_data: 请求数据
            
        Returns:
            bool: 发送是否成功
        """
        for attempt in range(self.retry_times):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        self.api_url,
                        json=request_data,
                        timeout=self.timeout
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            if result.get('success'):
                                return True
                            else:
                                self.logger.warning(f"API返回失败: {result.get('message', 'unknown error')}")
                        else:
                            self.logger.warning(f"HTTP错误: {response.status}")
                            
            except asyncio.TimeoutError:
                self.logger.warning(f"请求超时 (尝试 {attempt + 1}/{self.retry_times})")
            except Exception as e:
                self.logger.warning(f"请求异常 (尝试 {attempt + 1}/{self.retry_times}): {str(e)}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.retry_times - 1:
                await asyncio.sleep(self.retry_delay)
        
        return False
    
    def format_order_data(self, item_id, user_id, cookie_id, title="", amount=0.0):
        """
        格式化订单数据
        
        Args:
            item_id: 商品ID
            user_id: 用户ID
            cookie_id: Cookie账号ID
            title: 商品标题
            amount: 订单金额
            
        Returns:
            dict: 格式化后的订单数据
        """
        # 生成订单号
        timestamp = int(time.time())
        order_id = f"XY{timestamp}{user_id[-6:] if len(user_id) >= 6 else user_id}"
        
        # 生成支付链接
        payment_url = self.generate_payment_url(order_id, amount)
        
        return {
            'tb_order_sn': order_id,
            'tb_name': title or f"咸鱼商品-{item_id}",
            'tb_qr_url': payment_url,
            'sum': float(amount),
            'item_id': item_id,
            'user_id': user_id,
            'cookie_id': cookie_id,
            'is_auto': 1,
            'remark': f"自动监控订单 - {time.strftime('%Y-%m-%d %H:%M:%S')}"
        }
    
    def generate_payment_url(self, order_id, amount):
        """
        生成支付链接
        
        Args:
            order_id: 订单号
            amount: 金额
            
        Returns:
            str: 支付链接
        """
        try:
            import urllib.parse
            
            # 根据咸鱼URL模式生成支付链接
            base_url = "alipays://platformapi/startapp"
            app_id = "66666796"
            
            # 构建支付参数
            payment_params = {
                'orderId': order_id,
                'amount': amount,
                'timestamp': int(time.time()),
                'source': 'xianyu'
            }
            
            # 将参数编码为URL
            encoded_params = urllib.parse.urlencode(payment_params)
            payment_url = f"{base_url}?appId={app_id}&url={encoded_params}"
            
            return payment_url
            
        except Exception as e:
            self.logger.error(f"生成支付链接失败: {str(e)}")
            return f"https://www.goofish.com/item?id={order_id}"


# 使用示例和集成指南
def integration_example():
    """
    集成示例代码
    """
    print("="*80)
    print("咸鱼自动回复系统与代付系统集成示例")
    print("="*80)
    
    print("\n1. 在XianyuAutoAsync.py中添加以下代码:")
    print("-"*50)
    
    example_code = '''
# 在XianyuAutoAsync.py文件顶部添加导入
from xianyu_integration_config import PaymentSystemIntegration

# 在XianyuLive类的__init__方法中添加
def __init__(self, cookies_str=None, cookie_id: str = "default", user_id: int = None):
    # ... 现有代码 ...
    
    # 添加代付系统集成
    self.payment_integration = PaymentSystemIntegration(
        api_url="http://你的域名/api/auto_import_orders",  # 修改为你的API地址
        timeout=10
    )

# 修改extract_and_save_pending_order方法
async def extract_and_save_pending_order(self, message, user_id, item_id, msg_time):
    """提取并保存待支付订单信息"""
    try:
        # 获取商品信息
        item_info = await self.get_item_info(item_id)
        title = "咸鱼商品"
        amount = 0.0
        
        if item_info and 'data' in item_info:
            # 从API响应中提取商品信息
            data = item_info['data']
            if 'itemDO' in data:
                item_data = data['itemDO']
                title = item_data.get('title', title)
                # 提取价格信息
                if 'priceInfo' in item_data:
                    price_info = item_data['priceInfo']
                    amount = float(price_info.get('price', 0))
        
        # 格式化订单数据
        order_data = self.payment_integration.format_order_data(
            item_id=item_id,
            user_id=user_id,
            cookie_id=self.cookie_id,
            title=title,
            amount=amount
        )
        
        # 发送到代付系统
        success = await self.payment_integration.send_order_to_payment_system(order_data)
        
        if success:
            logger.info(f"[{msg_time}] 【订单同步】成功: {order_data['tb_order_sn']} - ¥{amount}")
        else:
            logger.error(f"[{msg_time}] 【订单同步】失败: {item_id}")
            
    except Exception as e:
        logger.error(f"处理待支付订单失败: {str(e)}")
'''
    
    print(example_code)
    
    print("\n2. 配置说明:")
    print("-"*50)
    print("• 修改api_url为你的PHP系统地址")
    print("• 确保PHP系统的AutoImport.php API正常运行")
    print("• 检查数据库表结构是否正确")
    print("• 测试网络连通性")
    
    print("\n3. 测试方法:")
    print("-"*50)
    print("• 启动Python系统: python Start.py")
    print("• 在咸鱼发送测试消息")
    print("• 观察日志输出")
    print("• 检查PHP系统数据库df_product表")


if __name__ == "__main__":
    integration_example()
