#!/usr/bin/env python3
"""
咸鱼支付系统API扩展
为PHP代付系统提供数据接口
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import asyncio
import json
import time
from datetime import datetime
import sqlite3
import re

# 请求模型
class SearchRequest(BaseModel):
    keyword: str
    page: int = 1
    page_size: int = 20

class QrCodeRequest(BaseModel):
    order_id: str

class CookieRequest(BaseModel):
    cookie_id: str
    cookie_value: str
    user_id: int = 1

# 响应模型
class OrderInfo(BaseModel):
    order_id: str
    title: str
    amount: float
    status: str
    qr_url: Optional[str] = None
    timestamp: int

class AccountInfo(BaseModel):
    id: str
    shop_name: str
    status: str
    last_check: int
    orders_today: int

class PaymentSystemAPI:
    """为PHP代付系统提供的API接口"""
    
    def __init__(self):
        self.db_path = "xianyu_payment.db"
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建待支付订单表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS pending_orders (
            order_id TEXT PRIMARY KEY,
            title TEXT,
            amount REAL,
            qr_url TEXT,
            status TEXT DEFAULT 'pending',
            created_at INTEGER,
            updated_at INTEGER
        )
        ''')
        
        # 创建账号监控表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS account_monitor (
            account_id TEXT PRIMARY KEY,
            shop_name TEXT,
            status TEXT DEFAULT 'active',
            last_check INTEGER,
            orders_today INTEGER DEFAULT 0,
            updated_at INTEGER
        )
        ''')
        
        conn.commit()
        conn.close()
    
    async def get_pending_orders(self) -> List[OrderInfo]:
        """获取待支付订单列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT order_id, title, amount, qr_url, status, created_at
            FROM pending_orders 
            WHERE status = 'pending'
            ORDER BY created_at DESC
            LIMIT 50
            ''')
            
            orders = []
            for row in cursor.fetchall():
                orders.append(OrderInfo(
                    order_id=row[0],
                    title=row[1] or "咸鱼订单",
                    amount=row[2],
                    qr_url=row[3],
                    status=row[4],
                    timestamp=row[5]
                ))
            
            conn.close()
            return orders
            
        except Exception as e:
            print(f"获取待支付订单失败: {e}")
            return []
    
    async def get_order_qr_code(self, order_id: str) -> Optional[str]:
        """获取订单二维码"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                'SELECT qr_url FROM pending_orders WHERE order_id = ?',
                (order_id,)
            )
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return result[0]
            
            # 如果数据库中没有，尝试实时获取
            qr_url = await self.fetch_qr_from_xianyu(order_id)
            if qr_url:
                # 保存到数据库
                await self.save_order_qr(order_id, qr_url)
                return qr_url
            
            return None
            
        except Exception as e:
            print(f"获取订单二维码失败: {e}")
            return None
    
    async def fetch_qr_from_xianyu(self, order_id: str) -> Optional[str]:
        """从咸鱼系统获取二维码（模拟实现）"""
        # 这里需要实际的咸鱼API调用
        # 目前返回模拟数据
        await asyncio.sleep(0.5)  # 模拟网络延迟

        # 模拟二维码URL
        mock_qr_url = f"https://qr.goofish.com/pay/{order_id}?amount=999&timestamp={int(time.time())}"
        return mock_qr_url

    async def get_pending_payment_orders(self) -> List[Dict[str, Any]]:
        """获取待支付订单信息（核心功能）"""
        try:
            # 这里应该通过WebSocket监控或API调用获取真实的待支付订单
            # 目前返回模拟数据，展示数据结构

            pending_orders = [
                {
                    'order_id': 'XY202501010001',
                    'amount': 1299.00,
                    'title': 'iPhone 15 Pro 256GB 深空黑色',
                    'buyer_name': '买家张三',
                    'seller_name': '数码专营店',
                    'create_time': int(time.time()) - 1800,  # 30分钟前创建
                    'expire_time': int(time.time()) + 1800,   # 30分钟后过期
                    'payment_url': self.generate_payment_url('XY202501010001', 1299.00),
                    'status': 'waiting_payment'
                },
                {
                    'order_id': 'XY202501010002',
                    'amount': 2599.00,
                    'title': 'MacBook Air M2 8GB+256GB 星光色',
                    'buyer_name': '买家李四',
                    'seller_name': '电脑配件店',
                    'create_time': int(time.time()) - 3600,  # 1小时前创建
                    'expire_time': int(time.time()) + 600,    # 10分钟后过期
                    'payment_url': self.generate_payment_url('XY202501010002', 2599.00),
                    'status': 'waiting_payment'
                },
                {
                    'order_id': 'XY202501010003',
                    'amount': 899.00,
                    'title': 'AirPods Pro 2代 USB-C',
                    'buyer_name': '买家王五',
                    'seller_name': '数码专营店',
                    'create_time': int(time.time()) - 900,   # 15分钟前创建
                    'expire_time': int(time.time()) + 2100,  # 35分钟后过期
                    'payment_url': self.generate_payment_url('XY202501010003', 899.00),
                    'status': 'waiting_payment'
                }
            ]

            return pending_orders

        except Exception as e:
            print(f"获取待支付订单失败: {e}")
            return []

    def generate_payment_url(self, order_id: str, amount: float) -> str:
        """生成支付链接（支付宝格式）"""
        # 根据你发现的URL模式生成支付链接
        base_url = "alipays://platformapi/startapp"
        app_id = "66666796"

        # 构建支付参数
        payment_params = {
            'orderId': order_id,
            'amount': amount,
            'timestamp': int(time.time()),
            'source': 'xianyu'
        }

        # 将参数编码为URL
        import urllib.parse
        encoded_params = urllib.parse.urlencode(payment_params)

        payment_url = f"{base_url}?appId={app_id}&url={encoded_params}"
        return payment_url

    async def auto_sync_to_payment_system(self, php_api_url: str = "http://localhost/api/auto_import_orders"):
        """自动同步订单到PHP代付系统"""
        try:
            # 获取待支付订单
            pending_orders = await self.get_pending_payment_orders()

            if not pending_orders:
                print("没有待支付订单")
                return

            # 准备同步数据
            sync_data = {
                'orders': [],
                'sync_time': int(time.time()),
                'source': 'xianyu_auto_sync'
            }

            for order in pending_orders:
                sync_data['orders'].append({
                    'tb_order_sn': order['order_id'],
                    'tb_name': order['title'],
                    'tb_qr_url': order['payment_url'],
                    'sum': order['amount'],
                    'buyer_name': order['buyer_name'],
                    'seller_name': order['seller_name'],
                    'expire_time': order['expire_time'],
                    'is_auto': 1,  # 标记为自动导入
                    'remark': f"自动同步 - {datetime.fromtimestamp(order['create_time']).strftime('%Y-%m-%d %H:%M:%S')}"
                })

            # 发送到PHP系统
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.post(php_api_url, json=sync_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"同步成功: {result}")
                        return True
                    else:
                        print(f"同步失败: HTTP {response.status}")
                        return False

        except Exception as e:
            print(f"自动同步失败: {e}")
            return False
    
    async def save_order_qr(self, order_id: str, qr_url: str):
        """保存订单二维码"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            INSERT OR REPLACE INTO pending_orders 
            (order_id, qr_url, updated_at) 
            VALUES (?, ?, ?)
            ''', (order_id, qr_url, int(time.time())))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"保存订单二维码失败: {e}")
    
    async def search_items(self, keyword: str, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """搜索闲鱼商品"""
        try:
            # 这里应该调用真实的商品搜索API
            # 目前返回模拟数据
            await asyncio.sleep(1)  # 模拟搜索延迟
            
            # 生成模拟商品数据
            items = []
            start_index = (page - 1) * page_size
            
            for i in range(page_size):
                item_index = start_index + i + 1
                price = 100 + (item_index * 50) + (hash(keyword) % 1000)
                
                items.append({
                    'item_id': f'XY{keyword[:2].upper()}{item_index:06d}',
                    'title': f'{keyword}相关商品 #{item_index}',
                    'price': f'¥{price}',
                    'seller_name': f'卖家{item_index}',
                    'item_url': f'https://www.goofish.com/item?id=XY{keyword[:2].upper()}{item_index:06d}',
                    'main_image': f'https://via.placeholder.com/200x200?text={keyword}商品{item_index}',
                    'publish_time': datetime.now().strftime('%Y-%m-%d %H:%M'),
                    'tags': [f'{keyword}', '热销', '包邮'],
                    'want_count': max(0, 100 - item_index)
                })
            
            return {
                'items': items,
                'total': 500 + hash(keyword) % 1000,
                'page': page,
                'page_size': page_size,
                'source': 'mock_api'
            }
            
        except Exception as e:
            print(f"搜索商品失败: {e}")
            return {'items': [], 'total': 0, 'error': str(e)}
    
    async def get_account_status(self) -> List[AccountInfo]:
        """获取账号状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT account_id, shop_name, status, last_check, orders_today
            FROM account_monitor
            ORDER BY updated_at DESC
            ''')
            
            accounts = []
            for row in cursor.fetchall():
                accounts.append(AccountInfo(
                    id=row[0],
                    shop_name=row[1] or "未知店铺",
                    status=row[2],
                    last_check=row[3] or int(time.time()),
                    orders_today=row[4]
                ))
            
            conn.close()
            
            # 如果没有数据，返回模拟数据
            if not accounts:
                accounts = [
                    AccountInfo(
                        id="account_001",
                        shop_name="数码专营店",
                        status="active",
                        last_check=int(time.time()) - 3600,
                        orders_today=15
                    ),
                    AccountInfo(
                        id="account_002",
                        shop_name="电脑配件店", 
                        status="expired",
                        last_check=int(time.time()) - 86400,
                        orders_today=0
                    )
                ]
            
            return accounts
            
        except Exception as e:
            print(f"获取账号状态失败: {e}")
            return []
    
    async def add_cookie(self, cookie_id: str, cookie_value: str, user_id: int = 1) -> bool:
        """添加Cookie到系统"""
        try:
            # 这里应该调用真实的Cookie管理API
            # 目前只是模拟保存
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
            INSERT OR REPLACE INTO account_monitor 
            (account_id, shop_name, status, last_check, updated_at) 
            VALUES (?, ?, ?, ?, ?)
            ''', (
                cookie_id, 
                f"店铺_{cookie_id}", 
                "active", 
                int(time.time()),
                int(time.time())
            ))
            
            conn.commit()
            conn.close()
            
            print(f"添加Cookie成功: {cookie_id}")
            return True
            
        except Exception as e:
            print(f"添加Cookie失败: {e}")
            return False

# 创建API实例
payment_api = PaymentSystemAPI()

# FastAPI路由（需要添加到现有的reply_server.py中）
async def setup_payment_routes(app: FastAPI):
    """设置支付系统相关的API路由"""
    
    @app.post("/api/get_pending_orders")
    async def api_get_pending_orders():
        """获取待支付订单"""
        orders = await payment_api.get_pending_orders()
        return {"orders": [order.dict() for order in orders]}
    
    @app.post("/api/get_qr_code")
    async def api_get_qr_code(request: QrCodeRequest):
        """获取订单二维码"""
        qr_url = await payment_api.get_order_qr_code(request.order_id)
        if qr_url:
            return {"qr_url": qr_url}
        else:
            raise HTTPException(status_code=404, detail="订单二维码未找到")
    
    @app.post("/api/search_items")
    async def api_search_items(request: SearchRequest):
        """搜索商品"""
        result = await payment_api.search_items(
            request.keyword, 
            request.page, 
            request.page_size
        )
        return result
    
    @app.get("/api/account_status")
    async def api_account_status():
        """获取账号状态"""
        accounts = await payment_api.get_account_status()
        return {"accounts": [account.dict() for account in accounts]}
    
    @app.post("/api/add_cookie")
    async def api_add_cookie(request: CookieRequest):
        """添加Cookie"""
        success = await payment_api.add_cookie(
            request.cookie_id,
            request.cookie_value, 
            request.user_id
        )
        return {"success": success}
    
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {"status": "ok", "timestamp": int(time.time())}

if __name__ == "__main__":
    # 测试API功能
    async def test_api():
        api = PaymentSystemAPI()
        
        # 测试搜索商品
        print("测试搜索商品...")
        items = await api.search_items("手机", 1, 5)
        print(f"搜索结果: {len(items['items'])} 个商品")
        
        # 测试获取账号状态
        print("\n测试获取账号状态...")
        accounts = await api.get_account_status()
        print(f"账号数量: {len(accounts)}")
        
        # 测试添加Cookie
        print("\n测试添加Cookie...")
        success = await api.add_cookie("test_001", "mock_cookie_value")
        print(f"添加Cookie: {'成功' if success else '失败'}")
    
    asyncio.run(test_api())
